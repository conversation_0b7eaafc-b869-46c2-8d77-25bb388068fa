import re
import random
import string
import hashlib
from datetime import datetime

def extract_mobile_number(content: str) -> str:
        """从内容中提取手机号"""
        # 移除所有空格和+号，方便后续匹配
        no_space_content = content.replace(' ', '').replace('+', '')
        # 查找一个非零数字开头的11位数字组合
        # 这能够正确处理前面带多个0的号码，例如从 "00085365252419" 提取 "85365252419"
        numbers = re.findall(r'[1-9]\d{10}', no_space_content)
        return numbers[0] if numbers else ''

def extract_us_phone_number(content: str) -> str:
        """从内容中提取美国手机号"""
        # 美国手机号格式：(XXX) XXX-XXXX 或 XXX-XXX-XXXX 或 XXX.XXX.XXXX 或 XXXXXXXXXX
        # 区号第一位不能是0或1，第二位不能是9
        # 移除所有非数字字符，保留原始格式用于匹配
        
        # 匹配各种格式的美国手机号
        patterns = [
            r'\b1?[-.\s]?\(?([2-9][0-8]\d)\)?[-.\s]?([2-9]\d{2})[-.\s]?(\d{4})\b',  # 标准格式
            r'\b1?([2-9][0-8]\d)([2-9]\d{2})(\d{4})\b'  # 连续数字格式
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            if matches:
                # 返回第一个匹配的号码，格式化为标准格式
                area_code, exchange, number = matches[0]
                return f"({area_code}) {exchange}-{number}"
        
        return ''

def get_weekday_from_date(year: int, month: int, day: int) -> str:
        """根据输入的年月日获取当天是星期几"""
        try:
            date_obj = datetime(year, month, day)
            weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            return weekdays[date_obj.weekday()]
        except ValueError:
            return 'Invalid date'

def generate_random_string(length: int = 15) -> str:
        """生成包含数字、大写字母和小写字母的随机字符串
        
        Args:
            length (int): 字符串长度，默认为15
            
        Returns:
            str: 生成的随机字符串
        """
        if length <= 0:
            return ''
        
        # 定义字符集：数字 + 大写字母 + 小写字母
        characters = string.digits + string.ascii_uppercase + string.ascii_lowercase
        
        # 生成随机字符串
        random_string = ''.join(random.choice(characters) for _ in range(length))
        
        return random_string

def generate_md5_hash(text: str, salt: str = "default_salt") -> str:
        """生成MD5哈希值
        
        Args:
            text (str): 要加密的文本
            salt (str): 盐值，默认为"default_salt"
            
        Returns:
            str: MD5哈希值（32位十六进制字符串）
        """
        if not isinstance(text, str):
            text = str(text)
        if not isinstance(salt, str):
            salt = str(salt)
        
        # 将文本和盐值组合
        salted_text = text + salt
        
        # 生成MD5哈希
        md5_hash = hashlib.md5(salted_text.encode('utf-8')).hexdigest()
        
        return md5_hash

def generate_md5_hash_with_custom_salt(text: str, salt: str) -> str:
        """使用自定义盐值生成MD5哈希值
        
        Args:
            text (str): 要加密的文本
            salt (str): 自定义盐值
            
        Returns:
            str: MD5哈希值（32位十六进制字符串）
        """
        return generate_md5_hash(text, salt)

print(extract_mobile_number("我的手机号 +133 1011 1010 "))
print(extract_mobile_number("我的手机号 00085 365 252419 "))
print(extract_mobile_number("我的手机号 +8536 525241 9 "))
print(extract_mobile_number("我的手机号 +0 00 85365252 419 "))

# 测试美国手机号提取
print("\n美国手机号测试:")
print(extract_us_phone_number("My phone is (*************"))
print(extract_us_phone_number("Call me at ************"))
print(extract_us_phone_number("Phone: ************"))
print(extract_us_phone_number("Contact: 5551234567"))
print(extract_us_phone_number("US number: +1 (*************"))
print(extract_us_phone_number("Invalid: (*************"))  # 应该返回空，因为区号不能以1开头

# 测试日期转星期几功能
print("\n日期转星期几测试:")
print(f"2024-01-01 is {get_weekday_from_date(2024, 1, 1)}")
print(f"2024-12-25 is {get_weekday_from_date(2024, 12, 25)}")
print(f"2023-07-15 is {get_weekday_from_date(2023, 7, 15)}")
print(f"Invalid date: {get_weekday_from_date(2024, 13, 32)}")

# 测试随机字符串生成功能
print("\n随机字符串生成测试:")
print(f"默认长度(15): {generate_random_string()}")
print(f"自定义长度(8): {generate_random_string(8)}")
print(f"自定义长度(20): {generate_random_string(20)}")
print(f"长度为0: '{generate_random_string(0)}'")

# 测试MD5哈希功能
print("\nMD5哈希测试:")
test_text = "Hello World"
print(f"原文: {test_text}")
print(f"使用默认salt: {generate_md5_hash(test_text)}")
print(f"使用自定义salt 'mysalt': {generate_md5_hash_with_custom_salt(test_text, 'mysalt')}")
print(f"使用自定义salt '123456': {generate_md5_hash_with_custom_salt(test_text, '123456')}")

# 测试不同文本的MD5
print(f"\n不同文本的MD5:")
print(f"'password' + 默认salt: {generate_md5_hash('password')}")
print(f"'password' + 'secret': {generate_md5_hash_with_custom_salt('password', 'secret')}")
print(f"'admin' + 'secret': {generate_md5_hash_with_custom_salt('admin', 'secret')}")

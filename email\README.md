# 临时邮箱工具

这是一个基于Python 3.8的GUI应用程序，可以自动获取临时邮箱地址并监控邮件内容。使用API协议模拟方案：

## 功能特性

- 🚀 **自动获取临时邮箱**: 点击按钮即可获取随机邮箱地址
- 📧 **实时邮件监控**: 每10秒自动检查新邮件
- 📋 **自动复制**: 邮箱地址自动复制到剪贴板
- 📝 **详细日志**: 实时显示操作日志和邮件内容
- 🔄 **高效方案**: API协议模拟，高效快速
- 🎯 **智能去重**: 自动过滤已获取的邮件

## 实现方案

### API协议模拟
- **优点**: 效率高，资源占用少，速度快
- **原理**: 直接分析网站的网络请求，模拟API调用
- **流程**: 
  1. 访问 https://www.singleuseemail.com/en 获取 csrf-token 和 cookies
  2. 访问 https://www.singleuseemail.com/messages 获取邮箱和邮件

## 安装要求

- Python 3.8+ (需要包含 tkinter，大多数 Python 安装都默认包含)

## 安装步骤

1. **克隆或下载项目文件**
2. **安装依赖包**:
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**:
   ```bash
   python main.py
   ```

## 快速开始

### Windows用户
```bash
# 双击运行
run.bat

# 或者命令行运行
python main.py
```

### Linux/Mac用户
```bash
# 设置权限并运行
chmod +x run.sh
./run.sh

# 或者直接运行
python3 main.py
```

### 演示模式
```bash
# 交互式演示
python demo.py

# 快速测试两种方案
python demo.py --quick

# 直接启动GUI
python demo.py --gui
```

## 使用方法

1. **启动程序**: 运行 `python main.py` 或使用快速启动脚本
2. **获取邮箱**: 点击"获取邮箱地址"按钮
3. **开始监控**: 点击"开始监控邮件"按钮
4. **查看日志**: 在右侧日志区域查看详细信息

## 界面说明

- **获取邮箱地址**: 生成新的临时邮箱
- **监控邮件内容**: 开始/停止邮件监控
- **当前邮箱**: 显示当前使用的邮箱地址
- **详细日志**: 显示所有操作日志和邮件内容
- **状态栏**: 显示当前程序状态

## 日志信息

程序会显示以下类型的日志：
- ✅ 邮箱地址已生成: <EMAIL>
- 📋 邮箱地址已自动复制到剪切板
- 📧 邮箱***********收到新邮件
- 👤 发件人: xxx
- 📝 主题: xxx
- 📄 内容预览: xxx

## 测试程序

运行测试脚本验证API方案的可用性：
```bash
python test_email_services.py
```

## 文件结构

```
├── main.py                    # 主程序入口
├── email_gui.py              # GUI界面
├── singleuse_email_api.py     # API协议模拟方案
├── test_email_services.py     # 测试脚本
├── requirements.txt           # 依赖包列表
└── README.md                 # 说明文档
```

## 依赖包说明

- `requests`: HTTP请求库，用于API方案
- `pyperclip`: 剪贴板操作
- `beautifulsoup4`: HTML解析
- `lxml`: XML/HTML解析器

## 故障排除

### 常见问题

1. **网络连接问题**
   - 检查网络连接
   - 确保可以访问 https://www.singleuseemail.com

2. **依赖包安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **tkinter 相关问题**
   - tkinter 是 Python 内置库，无需单独安装
   - 如果提示 tkinter 缺失，可能需要安装 python3-tk：
     ```bash
     # Ubuntu/Debian
     sudo apt-get install python3-tk
     
     # CentOS/RHEL
     sudo yum install tkinter
     
     # macOS (使用 Homebrew)
     brew install python-tk
     ```

4. **权限问题**
   - 在Windows上可能需要管理员权限
   - 在Linux/Mac上确保有执行权限

### 调试模式

如果遇到问题，可以：
1. 运行测试脚本查看详细错误信息
2. 检查日志输出
3. 检查网络连接状态

## 注意事项

- 临时邮箱仅用于临时接收邮件，不要用于重要账户
- 邮件内容可能被第三方查看，不要接收敏感信息
- 程序依赖于第三方网站，网站变更可能影响功能
- 建议在测试环境中先验证功能

## 更新日志

- v1.0.0: 初始版本，支持双重方案实现

## 许可证

本项目仅供学习和研究使用。
#!/usr/bin/env python3
"""
临时邮箱工具主程序
支持两种界面：
1. 现代化PyQt6界面（推荐）
2. 传统tkinter界面（兼容）

使用方法：
python main.py              # 启动PyQt6界面
python main.py --tkinter     # 启动tkinter界面
"""

import sys
import os
import argparse

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="临时邮箱工具")
    parser.add_argument("--tkinter", action="store_true", help="使用tkinter界面（兼容模式）")
    parser.add_argument("--qt6", action="store_true", help="使用PyQt6界面（默认）")

    args = parser.parse_args()

    # 默认使用PyQt5界面，除非明确指定其他选项
    use_tkinter = args.tkinter and not args.qt6
    use_qt6 = args.qt6 and not args.tkinter

    if use_tkinter:
        print("启动临时邮箱工具 - tkinter界面...")
        print("使用API协议模拟方案 - 高效、快速、资源占用少")
        print("-" * 50)

        try:
            from email_gui import EmailGUI
            app = EmailGUI()
            app.run()
        except ImportError as e:
            print(f"导入tkinter模块失败: {e}")
            print("请确保已安装所有依赖包：")
            print("pip install -r requirements.txt")
            sys.exit(1)
        except Exception as e:
            print(f"tkinter程序启动失败: {e}")
            sys.exit(1)
    elif use_qt6:
        print("启动临时邮箱工具 - PyQt6现代化界面...")
        print("功能特性：现代化设计、邮箱管理、邮件监控、数据库存储")
        print("-" * 50)

        try:
            from email_gui_qt6 import main as qt6_main
            qt6_main()
        except ImportError as e:
            print(f"导入PyQt6模块失败: {e}")
            print("请安装PyQt6依赖：")
            print("pip install PyQt6 aiosqlite")
            print("\n或者使用PyQt5模式：")
            print("python main.py")
            sys.exit(1)
        except Exception as e:
            print(f"PyQt6程序启动失败: {e}")
            print("\n尝试使用PyQt5模式：")
            print("python main.py")
            sys.exit(1)
    else:
        print("启动临时邮箱工具 - PyQt5现代化界面...")
        print("功能特性：现代化设计、邮箱管理、邮件监控、数据库存储")
        print("-" * 50)

        try:
            from email_gui_qt5 import main as qt5_main
            qt5_main()
        except ImportError as e:
            print(f"导入PyQt5模块失败: {e}")
            print("请安装PyQt5依赖：")
            print("pip install PyQt5 aiosqlite")
            print("\n或者使用兼容模式：")
            print("python main.py --tkinter")
            sys.exit(1)
        except Exception as e:
            print(f"PyQt5程序启动失败: {e}")
            print("\n尝试使用兼容模式：")
            print("python main.py --tkinter")
            sys.exit(1)


if __name__ == "__main__":
    main()
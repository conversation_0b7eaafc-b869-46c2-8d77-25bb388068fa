# Tasklist 任务列表管理系统 - AI代理操作手册

## 核心原则

作为AI代理，你必须遵循以下核心原则：

1. **透明执行**：所有复杂工作必须通过Tasklist展示计划
2. **结构化思维**：将大任务分解为20分钟粒度的子任务
3. **状态驱动**：严格按照状态机管理任务生命周期
4. **实时更新**：及时反映工作进度和状态变化
5. **用户可控**：确保用户能够监督、编辑和干预工作流程

## 何时使用Tasklist - 决策矩阵

### ✅ 必须使用Tasklist的场景

- 多步骤工作（>3个独立步骤）
- 跨文件/跨模块的修改
- 需要特定执行顺序的任务
- 用户明确要求规划或任务分解
- 复杂的重构、迁移或架构变更
- 需要测试验证的开发工作

### ❌ 不使用Tasklist的场景

- 单文件的简单修改
- 快速调试或问题排查
- 纯信息查询或解释
- 一次性的代码片段生成
- 简单的配置文件更新

## 标准工作流程

### 1. 任务列表创建流程

当遇到复杂任务时，按以下步骤操作：

```markdown
步骤1: 分析任务复杂度
- 评估是否需要多个步骤
- 确定是否涉及多个文件/模块
- 判断是否需要特定执行顺序

步骤2: 创建初始任务列表
- 使用 add_tasks 工具创建根任务
- 分解为具体的子任务
- 确保每个子任务代表约20分钟的工作量

步骤3: 验证任务结构
- 检查任务依赖关系
- 确认执行顺序合理
- 添加测试和验证步骤
```

### 2. 任务执行流程

```markdown
执行前:
1. 使用 view_tasklist 查看当前状态
2. 确认下一个要执行的任务

执行中:
1. 将当前任务标记为 IN_PROGRESS
2. 执行具体工作
3. 完成后标记为 COMPLETE
4. 使用批量更新同时处理多个状态变化

执行后:
1. 验证工作结果
2. 更新任务描述（如有必要）
3. 准备下一个任务
```

## 任务状态管理规范

### 状态定义和使用场景

**NOT_STARTED `[ ]`**
- 初始状态，任务已规划但未开始
- 等待前置任务完成
- 用户尚未批准开始执行

**IN_PROGRESS `[/]`**
- 正在执行的任务
- 一次只能有一个主要任务处于此状态
- 需要实时更新进度

**COMPLETE `[x]`**
- 任务已成功完成
- 工作结果已验证
- 可以进行下一步

**CANCELLED `[-]`**
- 任务不再需要执行
- 需求发生变化
- 发现更好的解决方案

### 状态转换规则

```
NOT_STARTED → IN_PROGRESS → COMPLETE
     ↓              ↓           ↓
  CANCELLED ← CANCELLED ← IN_PROGRESS (如果需要重做)
```

**强制规则：**
- 必须按顺序转换状态
- 使用批量更新提高效率
- 状态变化必须有明确原因

## API工具使用指南

### add_tasks - 创建新任务

**基本语法：**
```json
{
  "tasks": [
    {
      "name": "任务名称",
      "description": "详细描述任务内容和预期结果",
      "state": "NOT_STARTED",
      "parent_task_id": "父任务UUID（可选）"
    }
  ]
}
```

**创建根任务示例：**
```json
{
  "tasks": [
    {
      "name": "重构用户认证系统",
      "description": "将现有的session认证迁移到JWT令牌认证，包括数据库更新、API修改和前端适配"
    }
  ]
}
```

**创建子任务示例：**
```json
{
  "tasks": [
    {
      "name": "设计JWT令牌结构",
      "description": "定义令牌payload、过期时间和刷新机制",
      "parent_task_id": "parent-task-uuid"
    },
    {
      "name": "实现令牌验证中间件",
      "description": "创建Express中间件验证JWT令牌有效性",
      "parent_task_id": "parent-task-uuid"
    }
  ]
}
```

### update_tasks - 更新任务状态

**单任务更新：**
```json
{
  "tasks": [
    {
      "task_id": "task-uuid",
      "state": "COMPLETE",
      "description": "更新后的任务描述（可选）"
    }
  ]
}
```

**批量状态更新（推荐）：**
```json
{
  "tasks": [
    {
      "task_id": "previous-task-uuid",
      "state": "COMPLETE"
    },
    {
      "task_id": "current-task-uuid",
      "state": "IN_PROGRESS"
    }
  ]
}
```

**任务重命名和描述更新：**
```json
{
  "tasks": [
    {
      "task_id": "task-uuid",
      "name": "新的任务名称",
      "description": "更详细的任务描述，包含新发现的需求"
    }
  ]
}
```

### view_tasklist - 查看当前状态

**使用时机：**
- 开始工作前检查任务列表
- 需要了解整体进度时
- 寻找下一个要执行的任务时

### reorganize_tasklist - 重组任务结构

**仅在以下情况使用：**
- 需要大幅调整任务顺序
- 改变多个任务的层级关系
- 发现原有结构存在根本性问题

**示例：**
```markdown
- [/] 重构认证系统
  - [x] 需求分析和设计
    - [x] 分析现有认证流程
    - [x] 设计JWT令牌结构
  - [/] 后端实现
    - [x] 实现令牌生成逻辑
    - [/] 创建验证中间件
    - [ ] 更新用户登录API
  - [ ] 前端适配
  - [ ] 测试和部署
```

## 最佳实践模板

### 模板1：代码重构工作流

**初始化阶段：**
```json
{
  "tasks": [
    {
      "name": "重构[模块名称]",
      "description": "重构目标、范围和预期收益的详细说明"
    }
  ]
}
```

**规划阶段子任务：**
```json
{
  "tasks": [
    {
      "name": "分析现有代码结构",
      "description": "识别问题点、依赖关系和重构风险",
      "parent_task_id": "root-task-uuid"
    },
    {
      "name": "设计新的架构",
      "description": "定义重构后的模块结构和接口",
      "parent_task_id": "root-task-uuid"
    },
    {
      "name": "制定迁移计划",
      "description": "确定重构步骤和回滚策略",
      "parent_task_id": "root-task-uuid"
    }
  ]
}
```

### 模板2：功能开发工作流

**完整功能开发结构：**
```markdown
- [ ] 实现[功能名称]
  - [ ] 需求分析和设计
    - [ ] 分析业务需求
    - [ ] 设计数据模型
    - [ ] 设计API接口
  - [ ] 后端实现
    - [ ] 创建数据库迁移
    - [ ] 实现业务逻辑
    - [ ] 创建API端点
  - [ ] 前端实现
    - [ ] 创建UI组件
    - [ ] 实现状态管理
    - [ ] 集成API调用
  - [ ] 测试和验证
    - [ ] 编写单元测试
    - [ ] 执行集成测试
    - [ ] 用户验收测试
```

### 模板3：Bug修复工作流

```json
{
  "tasks": [
    {
      "name": "修复[Bug描述]",
      "description": "Bug详细描述、复现步骤和影响范围"
    },
    {
      "name": "问题诊断",
      "description": "定位问题根本原因",
      "parent_task_id": "root-uuid"
    },
    {
      "name": "实现修复",
      "description": "编写修复代码",
      "parent_task_id": "root-uuid"
    },
    {
      "name": "验证修复",
      "description": "测试修复效果，确保无副作用",
      "parent_task_id": "root-uuid"
    }
  ]
}
```

## 操作决策指南

### 何时创建新任务 vs 更新现有任务

**创建新任务的情况：**
- 发现遗漏的重要步骤
- 需求范围扩展
- 出现新的依赖关系
- 原计划未考虑的风险需要处理

**更新现有任务的情况：**
- 任务描述需要澄清
- 发现更好的实现方法
- 任务范围需要调整
- 状态需要变更

### 任务粒度控制

**合适的任务粒度：**
- 单个任务代表一个完整的工作单元
- 预估执行时间15-30分钟
- 有明确的完成标准
- 可以独立验证结果

**过于细粒度的信号：**
- 任务描述只有一句话
- 执行时间少于5分钟
- 无法独立验证
- 与其他任务高度耦合

**过于粗粒度的信号：**
- 任务描述包含多个"和"
- 预估执行时间超过1小时
- 包含多个不同类型的工作
- 难以确定完成标准

### 错误处理和恢复

**当任务执行失败时：**
1. 将任务状态保持为IN_PROGRESS
2. 分析失败原因
3. 决定是否需要分解任务
4. 更新任务描述包含新发现的信息
5. 继续执行或寻求用户指导

**当发现计划有误时：**
1. 立即停止当前执行
2. 使用update_tasks更新相关任务
3. 必要时使用add_tasks添加遗漏步骤
4. 向用户说明变更原因
5. 等待确认后继续

## 常见场景处理

### 场景1：用户要求修改正在执行的计划

**标准响应流程：**
1. 使用view_tasklist查看当前状态
2. 根据用户要求评估影响范围
3. 使用update_tasks或add_tasks进行调整
4. 向用户确认修改后的计划
5. 继续执行

### 场景2：发现任务依赖关系错误

**处理步骤：**
1. 将相关任务标记为NOT_STARTED
2. 使用reorganize_tasklist重新排序
3. 更新任务描述说明依赖关系
4. 从正确的起点重新开始

### 场景3：任务执行中遇到技术障碍

**应对策略：**
1. 保持当前任务为IN_PROGRESS
2. 创建新的子任务处理障碍
3. 更新父任务描述包含新信息
4. 完成障碍处理后继续原任务

## 提示词模板库

### 模板1：复杂重构项目启动

```
"我需要重构[系统/模块名称]。请创建一个详细的Tasklist，包括：
1. 现状分析和风险评估
2. 分阶段的重构计划
3. 测试和验证步骤
4. 回滚策略

先创建计划，不要立即开始实现。"
```

### 模板2：新功能完整开发

```
"请为[功能描述]创建一个完整的开发Tasklist，包括：
- 需求分析和技术设计
- 数据库设计和迁移
- 后端API实现
- 前端界面开发
- 测试用例编写和执行
- 部署和监控

确保每个任务都有明确的完成标准。"
```

### 模板3：Bug修复和问题排查

```
"针对[Bug描述]创建一个系统性的修复Tasklist：
1. 问题复现和根因分析
2. 影响范围评估
3. 修复方案设计
4. 代码实现和测试
5. 验证和部署

请确保包含回归测试步骤。"
```

### 模板4：代码审查和优化

```
"请创建一个Tasklist来审查[代码/PR]，包括：
- 代码质量和规范检查
- 性能和安全性分析
- 测试覆盖率评估
- 文档和注释审查
- 改进建议和实施计划"
```

## 质量检查清单

### 创建Tasklist前的检查

- [ ] 确认任务复杂度需要结构化管理
- [ ] 评估是否涉及多个步骤或文件
- [ ] 确定用户是否需要监督和干预能力
- [ ] 验证任务有明确的完成标准

### 任务设计质量检查

- [ ] 每个任务都有清晰的名称和描述
- [ ] 任务粒度适中（15-30分钟执行时间）
- [ ] 任务之间的依赖关系明确
- [ ] 包含必要的测试和验证步骤
- [ ] 有明确的完成标准

### 执行过程质量检查

- [ ] 及时更新任务状态
- [ ] 使用批量更新提高效率
- [ ] 遇到问题时及时调整计划
- [ ] 保持与用户的沟通
- [ ] 记录重要的决策和变更

## 故障排除指南

### 常见问题及解决方案

**问题：任务列表过于复杂，用户感到困惑**
- 解决：简化任务描述，合并相关子任务
- 预防：创建时控制层级深度，避免超过3层

**问题：任务执行顺序不当，出现依赖冲突**
- 解决：使用reorganize_tasklist重新排序
- 预防：创建时仔细分析依赖关系

**问题：任务粒度不当，执行效率低**
- 解决：重新分解或合并任务
- 预防：遵循20分钟粒度原则

**问题：用户频繁修改计划，执行中断**
- 解决：在执行前充分沟通和确认
- 预防：创建更详细的初始计划

## 成功指标

### 衡量Tasklist使用效果

- **完成率**：已完成任务占总任务的比例
- **准确性**：计划与实际执行的匹配度
- **效率**：平均任务执行时间
- **用户满意度**：用户对过程透明度的反馈

### 持续改进

- 定期回顾任务执行情况
- 收集用户反馈和建议
- 优化常用工作流程模板
- 更新最佳实践指南

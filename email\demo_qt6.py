#!/usr/bin/env python3
"""
PyQt6界面演示脚本
展示新的现代化界面功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🎉 临时邮箱工具 - PyQt6现代化界面演示")
    print("=" * 60)
    print()

def print_features():
    """打印功能特性"""
    print("🎨 界面特性:")
    print("  ✅ 现代化扁平设计风格")
    print("  ✅ 圆角按钮和柔和阴影")
    print("  ✅ 蓝色主题配色方案")
    print("  ✅ 响应式布局设计")
    print()
    
    print("📱 多页面布局:")
    print("  📧 邮箱管理 - 邮箱列表管理，支持添加、编辑、删除")
    print("  📬 邮件查看 - 邮件列表和内容查看，支持筛选和删除")
    print("  ⚙️ 设置 - 应用配置和数据库管理")
    print()
    
    print("🚀 新功能特性:")
    print("  ✅ 一键获取新邮箱地址")
    print("  ✅ 邮箱状态管理（激活/未激活）")
    print("  ✅ 邮箱类型设置（主账号/团队账号）")
    print("  ✅ 独立邮箱监控（支持多邮箱同时监控）")
    print("  ✅ 邮件列表显示和筛选")
    print("  ✅ 邮件内容详细查看")
    print("  ✅ SQLite数据库存储")
    print("  ✅ 异步数据库操作")
    print()

def print_usage():
    """打印使用说明"""
    print("🎯 使用方法:")
    print("  # 启动PyQt6界面（默认）")
    print("  python main.py")
    print()
    print("  # 或明确指定PyQt6")
    print("  python main.py --qt6")
    print()
    print("  # 使用传统tkinter界面")
    print("  python main.py --tkinter")
    print()
    print("  # 使用专用启动脚本")
    print("  python run_qt6.py")
    print()

def print_tech_stack():
    """打印技术栈"""
    print("🔧 技术架构:")
    print("  前端界面:")
    print("    • PyQt6 - 现代化GUI框架")
    print("    • 自定义组件 - ModernButton、EmailCard等")
    print("    • 响应式布局 - QSplitter、QTabWidget")
    print("    • 现代样式 - 完整的QSS样式表")
    print()
    print("  后端逻辑:")
    print("    • 异步数据库 - aiosqlite + SQLite")
    print("    • 多线程监控 - QThread邮件监控")
    print("    • API集成 - SingleUseEmailAPI")
    print("    • 数据管理 - DatabaseManager")
    print()
    print("  数据存储:")
    print("    • 邮箱表 - emails (邮箱信息和状态)")
    print("    • 邮件表 - email_messages (邮件内容)")
    print("    • 关系约束 - 外键关联和级联删除")
    print()

def check_dependencies():
    """检查依赖"""
    print("📦 依赖检查:")
    
    missing = []
    
    try:
        import PyQt6
        print("  ✅ PyQt6 已安装")
    except ImportError:
        print("  ❌ PyQt6 未安装")
        missing.append("PyQt6")
    
    try:
        import aiosqlite
        print("  ✅ aiosqlite 已安装")
    except ImportError:
        print("  ❌ aiosqlite 未安装")
        missing.append("aiosqlite")
    
    try:
        import requests
        print("  ✅ requests 已安装")
    except ImportError:
        print("  ❌ requests 未安装")
        missing.append("requests")
    
    try:
        import pyperclip
        print("  ✅ pyperclip 已安装")
    except ImportError:
        print("  ❌ pyperclip 未安装")
        missing.append("pyperclip")
    
    print()
    
    if missing:
        print("❌ 缺少依赖包，请运行:")
        print(f"  pip install {' '.join(missing)}")
        return False
    else:
        print("✅ 所有依赖包已安装")
        return True

def main():
    """主函数"""
    print_banner()
    print_features()
    print_usage()
    print_tech_stack()
    
    if check_dependencies():
        print()
        print("🚀 准备启动PyQt6界面...")
        print("提示：关闭演示窗口后，您可以使用上述命令启动完整应用")
        print()
        
        try:
            from email_gui_qt6 import main as qt6_main
            qt6_main()
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            print("请检查依赖包是否正确安装")
    else:
        print()
        print("❌ 请先安装缺少的依赖包")

if __name__ == "__main__":
    main()

"""
SingleUse Email API 实现
基于新的API流程：
1. 访问 https://www.singleuseemail.com/en 获取 csrf-token 和 cookies
2. 访问 https://www.singleuseemail.com/messages 获取邮箱和邮件
"""

import requests
import json
import re
import time
import gzip
import zlib
import sqlite3
import os
import asyncio
import aiosqlite
import pickle
import base64
from datetime import datetime
from typing import Dict, List, Optional, Tuple


class SingleUseEmailAPI:
    """SingleUse Email API 客户端"""
    
    def __init__(self):
        """初始化API客户端"""
        self.base_url = "https://www.singleuseemail.com"
        self.session = requests.Session()
        self.current_email = None
        self.csrf_token = None
        
        # 设置请求头（参考c_en.py的设置）
        self.session.headers.update({
            'authority': 'www.singleuseemail.com',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'max-age=0',
            'sec-ch-ua': '"Chromium";v="118", "Google Chrome";v="118", "Not=A?Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
        })
        
        print("✅ SingleUse Email API 客户端已初始化")

    def serialize_cookies(self) -> str:
        """序列化session cookies为字符串"""
        try:
            cookies_dict = dict(self.session.cookies)
            cookies_json = json.dumps(cookies_dict)
            cookies_b64 = base64.b64encode(cookies_json.encode('utf-8')).decode('utf-8')
            return cookies_b64
        except Exception as e:
            print(f"❌ 序列化cookies失败: {e}")
            return ""

    def restore_cookies(self, cookies_str: str) -> bool:
        """从字符串恢复session cookies"""
        try:
            if not cookies_str:
                print("⚠️ cookies字符串为空")
                return False

            cookies_json = base64.b64decode(cookies_str.encode('utf-8')).decode('utf-8')
            cookies_dict = json.loads(cookies_json)

            # 清除现有cookies
            self.session.cookies.clear()

            # 恢复cookies
            for name, value in cookies_dict.items():
                self.session.cookies.set(name, value)

            print(f"✅ 恢复cookies成功: {len(cookies_dict)} 个")
            print(f"🍪 恢复的cookies: {list(cookies_dict.keys())}")
            return True

        except Exception as e:
            print(f"❌ 恢复cookies失败: {e}")
            return False

    def get_session_info(self) -> Tuple[Optional[str], Optional[str]]:
        """获取当前session信息（token和cookies）"""
        try:
            token = self.csrf_token
            cookies = self.serialize_cookies()
            return token, cookies
        except Exception as e:
            print(f"❌ 获取session信息失败: {e}")
            return None, None

    def restore_session(self, token: str, cookies: str) -> bool:
        """恢复完整的session状态"""
        try:
            print(f"🔄 恢复session状态...")
            print(f"🔑 Token: {token[:20]}..." if token else "🔑 Token: None")
            print(f"🍪 Cookies长度: {len(cookies) if cookies else 0}")

            # 恢复token
            self.csrf_token = token

            # 恢复cookies
            if cookies and self.restore_cookies(cookies):
                # 更新请求头
                self.session.headers.update({
                    'X-CSRF-TOKEN': self.csrf_token,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': f"{self.base_url}/en",
                })

                print("✅ Session状态恢复成功")
                return True
            else:
                print("❌ Cookies恢复失败")
                return False

        except Exception as e:
            print(f"❌ 恢复session状态失败: {e}")
            return False

    def set_session(self, token: str, cookies: str) -> bool:
        """设置session信息（为了兼容性，调用restore_session）"""
        return self.restore_session(token, cookies)



    def _extract_csrf_token(self, html_content: str) -> Optional[str]:
        """从HTML内容中提取CSRF token"""
        try:
            # 确保内容是字符串格式
            if isinstance(html_content, bytes):
                html_content = html_content.decode('utf-8', errors='ignore')
            
            # 多种模式尝试提取csrf-token
            patterns = [
                # 标准格式：<meta name="csrf-token" content="...">
                r'<meta name="csrf-token" content="([^"]+)"',
                # 可能的变体格式
                r'<meta name=["\']csrf-token["\'] content=["\']([^"\']+)["\']',
                r'<meta[^>]*name=["\']csrf-token["\'][^>]*content=["\']([^"\']+)["\']',
                r'<meta[^>]*content=["\']([^"\']+)["\'][^>]*name=["\']csrf-token["\']',
                # 更宽泛的搜索
                r'csrf-token["\'][^>]*content=["\']([^"\']+)["\']',
                r'name=["\']csrf-token["\'][^>]*content=["\']([^"\']+)["\']'
            ]
            
            print("开始尝试提取CSRF token...")
            for i, pattern in enumerate(patterns, 1):
                print(f"尝试模式{i}: {pattern}")
                csrf_match = re.search(pattern, html_content, re.IGNORECASE | re.DOTALL)
                if csrf_match:
                    token = csrf_match.group(1).strip()
                    print(f"✅ 使用模式{i}提取到CSRF token: {token}")
                    
                    # 保存匹配的完整内容到文件
                    """ try:
                        with open('debug_csrf_match.txt', 'w', encoding='utf-8') as f:
                            f.write(f"匹配模式: {pattern}\n")
                            f.write(f"匹配内容: {csrf_match.group(0)}\n")
                            f.write(f"提取的token: {token}\n")
                        print("✅ CSRF匹配信息已保存到 debug_csrf_match.txt")
                    except Exception as e:
                        print(f"保存CSRF匹配信息失败: {e}") """
                    
                    return token
            
            print("❌ 所有模式都未找到CSRF token")
            
            # 尝试手动搜索包含csrf的所有内容
            csrf_lines = []
            for line in html_content.split('\n'):
                if 'csrf' in line.lower():
                    csrf_lines.append(line.strip())
            
            """ if csrf_lines:
                try:
                    with open('debug_csrf_lines.txt', 'w', encoding='utf-8') as f:
                        f.write("包含'csrf'的所有行:\n")
                        for i, line in enumerate(csrf_lines, 1):
                            f.write(f"{i}: {line}\n")
                    print(f"✅ 找到{len(csrf_lines)}行包含'csrf'的内容，已保存到 debug_csrf_lines.txt")
                except Exception as e:
                    print(f"保存csrf行失败: {e}") """
            
            return None
        except Exception as e:
            print(f"❌ 提取CSRF token失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _get_initial_data(self) -> bool:
        """获取初始数据（CSRF token和cookies）"""
        try:
            print("🔄 正在访问 https://www.singleuseemail.com/en 获取初始数据...")
            
            # 首次访问主页，不设置任何cookies，让服务器返回新的cookies
            # 增加超时时间和重试机制
            for attempt in range(3):
                try:
                    response = self.session.get(f"{self.base_url}/en", timeout=30)
                    response.raise_for_status()
                    break
                except Exception as e:
                    print(f"❌ 访问尝试 {attempt + 1} 失败: {e}")
                    if attempt < 2:
                        print("⏳ 等待5秒后重试...")
                        time.sleep(5)
                    else:
                        raise
            
            print(f"✅ 页面访问成功，状态码: {response.status_code}")
            
            # 动态获取并设置cookies
            received_cookies = dict(response.cookies)
            print(f"✅ 从响应中获取到cookies: {received_cookies}")
            
            # 将获取到的cookies更新到session中，这样后续请求会自动使用
            self.session.cookies.update(response.cookies)
            print(f"✅ 已将cookies更新到session中")
            
            # 简化处理，直接使用response.text（像c_en.py一样）
            print(f"响应编码: {response.encoding}")
            print(f"响应头Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            print(f"响应内容长度: {len(response.content)} 字节")
            
            # 直接使用response.text，让requests自动处理编码和解压缩
            html_content = response.text
            print(f"✅ 使用response.text获取内容成功")
            print(f"HTML内容长度: {len(html_content)} 字符")
            
            # 保存网页内容用于调试
            try:
                #with open('debug_page_content.html', 'w', encoding='utf-8') as f:
                #    f.write(html_content)
                #print("✅ 网页内容已保存到 debug_page_content.html")
                
                # 打印前200字符用于快速查看
                print("网页内容预览:")
                print(html_content[:200])
                print("=" * 50)
            except Exception as e:
                print(f"保存网页内容失败: {e}")
            
            # 直接提取CSRF token，不需要额外的HEAD部分处理
            
            # 提取CSRF token
            self.csrf_token = self._extract_csrf_token(html_content)
            if not self.csrf_token:
                print("❌ 无法获取CSRF token")
                print("尝试在整个页面中搜索 csrf-token...")
                # 尝试更宽泛的搜索
                csrf_matches = re.findall(r'csrf[^"\']*["\'][^"\']*["\']', html_content, re.IGNORECASE)
                if csrf_matches:
                    print(f"找到可能的csrf相关内容: {csrf_matches[:5]}")
                
                # 搜索所有meta标签并保存
                meta_matches = re.findall(r'<meta[^>]*>', html_content, re.IGNORECASE)
                print(f"找到 {len(meta_matches)} 个meta标签:")
                
                """ try:
                    with open('debug_meta_tags.txt', 'w', encoding='utf-8') as f:
                        for i, meta in enumerate(meta_matches, 1):
                            f.write(f"{i}: {meta}\n")
                    print("✅ 所有meta标签已保存到 debug_meta_tags.txt")
                    
                    # 显示前10个
                    for i, meta in enumerate(meta_matches[:10]):
                        print(f"  {i+1}: {meta}")
                except Exception as e:
                    print(f"保存meta标签失败: {e}") """
                
                return False
            
            # 更新请求头，添加必要的头部
            self.session.headers.update({
                'X-CSRF-TOKEN': self.csrf_token,
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': f"{self.base_url}/en",
            })
            
            return True
            
        except Exception as e:
            print(f"❌ 获取初始数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_email_and_messages(self) -> Tuple[Optional[str], List[Dict]]:
        """获取邮箱地址和邮件列表"""
        try:
            # 获取初始数据
            if not self._get_initial_data():
                return None, []

            print("🔄 正在访问 /messages 接口获取邮箱和邮件...")
            print(f" 使用的token::: {self.csrf_token} ")
            # 准备POST数据
            data = {
                '_token': self.csrf_token
            }

            # 调用messages接口，增加超时时间
            response = self.session.post(f"{self.base_url}/messages", data=data, timeout=30)
            response.raise_for_status()

            print(f"✅ messages接口访问成功，状态码: {response.status_code}")

            try:
                result = response.json()
                print(f"✅ 接口返回数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except:
                print(f"❌ 响应不是JSON格式: {response.text[:500]}")
                return None, []

            # 提取邮箱地址
            mailbox = result.get('mailbox')
            if mailbox:
                self.current_email = mailbox
                print(f"✅ 获取到邮箱地址: {self.current_email}")
            else:
                print("❌ 未获取到邮箱地址")
                return None, []

            # 提取邮件列表
            messages = result.get('messages', [])
            print(f"📧 获取到 {len(messages)} 封邮件")

            return self.current_email, messages

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None, []
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"响应内容: {response.text[:500]}")
            return None, []
        except Exception as e:
            print(f"❌ 获取邮箱和邮件失败: {e}")
            return None, []

    def get_email_with_session(self) -> Tuple[Optional[str], List[Dict], Optional[str], Optional[str]]:
        """获取邮箱地址、邮件列表以及session信息（token和cookies）"""
        try:
            email, messages = self.get_email_and_messages()
            if email:
                token, cookies = self.get_session_info()
                print(f"🔑 获取到session信息 - Token: {token[:20] if token else 'None'}..., Cookies: {len(cookies) if cookies else 0} 字符")
                return email, messages, token, cookies
            else:
                return None, [], None, None
        except Exception as e:
            print(f"❌ 获取邮箱和session信息失败: {e}")
            return None, [], None, None
    
    def get_email_address(self) -> Optional[str]:
        """获取新的临时邮箱地址"""
        email, _ = self.get_email_and_messages()
        return email
    
    def get_messages(self) -> List[Dict]:
        """获取邮件列表"""
        if not self.current_email or not self.csrf_token:
            # 如果没有当前邮箱或token，尝试重新获取
            _, messages = self.get_email_and_messages()
            return messages

        try:
            print("🔄 正在检查邮件...")
            print(f" 使用的token::: {self.csrf_token} ")
            # 准备POST数据
            data = {
                '_token': self.csrf_token
            }

            # 调用messages接口，增加超时时间
            response = self.session.post(f"{self.base_url}/messages", data=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            messages = result.get('messages', [])
            print(f"📧 找到 {len(messages)} 封邮件")

            return messages

        except Exception as e:
            print(f"❌ 获取邮件失败: {e}")
            print(f"🔍 错误详情: {type(e).__name__}: {str(e)}")

            # 如果是token相关错误（包括419认证失败错误），直接抛出异常让监控线程处理
            # 不再尝试恢复，严格按照固定token策略
            if "419" in str(e) or "proxy reauthentication" in str(e) or "token" in str(e).lower():
                print("❌ 检测到token错误，按照固定token策略直接停止监控")
                print(f"❌ 失效的token: {self.csrf_token}")
                raise e  # 重新抛出异常，让监控线程捕获并停止

            return []
    
    def format_message(self, message: Dict) -> str:
        """格式化邮件信息"""
        try:
            subject = message.get('subject', '无主题')
            sender = message.get('from', '未知发件人')
            sender_email = message.get('from_email', '')
            received_at = message.get('receivedAt', '未知时间')
            
            # 构建发件人信息
            from_info = f"{sender} <{sender_email}>" if sender_email else sender
            
            formatted = f"""
📧 新邮件
👤 发件人: {from_info}
📝 主题: {subject}
🕒 时间: {received_at}
"""
            
            # 如果有内容预览，添加内容
            if 'content' in message:
                content = message['content']
                # 简单清理HTML标签
                content_clean = re.sub(r'<[^>]+>', '', content)
                content_preview = content_clean[:200] + "..." if len(content_clean) > 200 else content_clean
                formatted += f"📄 内容预览: {content_preview}\n"
            
            return formatted.strip()
            
        except Exception as e:
            print(f"❌ 格式化邮件失败: {e}")
            return f"邮件格式化失败: {str(message)}"
    
    def monitor_emails(self, callback=None, interval: int = 10) -> List[Dict]:
        """监控邮件（单次检查）"""
        try:
            emails = self.get_messages()
            
            if callback and emails:
                for email in emails:
                    callback(email)
            
            return emails
            
        except Exception as e:
            print(f"❌ 监控邮件失败: {e}")
            return []
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            print("🔄 正在测试连接...")
            # 增加超时时间，添加重试机制
            for attempt in range(3):
                try:
                    response = self.session.get(self.base_url, timeout=30)
                    response.raise_for_status()
                    print("✅ 连接测试成功")
                    return True
                except Exception as e:
                    print(f"❌ 连接尝试 {attempt + 1} 失败: {e}")
                    if attempt < 2:
                        print("⏳ 等待5秒后重试...")
                        time.sleep(5)
                    continue
            return False
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            return False


def test_api():
    """测试API功能"""
    print("=" * 50)
    print("SingleUse Email API 测试")
    print("=" * 50)
    
    api = SingleUseEmailAPI()
    
    # 测试连接
    if not api.test_connection():
        print("❌ 连接失败，无法继续测试")
        return False
    
    # 获取邮箱地址和邮件
    email, emails = api.get_email_and_messages()
    if not email:
        print("❌ 获取邮箱失败")
        return False
    
    print(f"✅ 测试邮箱: {email}")
    
    if emails:
        print(f"📧 找到 {len(emails)} 封邮件:")
        for i, email_data in enumerate(emails, 1):
            print(f"\n邮件 {i}:")
            print(api.format_message(email_data))
    else:
        print("📭 暂无邮件")
    
    print("\n✅ API测试完成")
    return True


if __name__ == "__main__":
    test_api()
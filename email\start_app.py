#!/usr/bin/env python3
"""
临时邮箱工具启动脚本
自动激活虚拟环境并启动PyQt6界面
"""

import sys
import os
import subprocess

def main():
    """主函数"""
    print("🚀 启动临时邮箱工具...")
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    venv_python = os.path.join(project_root, ".venv", "Scripts", "python.exe")
    
    # 检查虚拟环境Python是否存在
    if not os.path.exists(venv_python):
        print(f"❌ 虚拟环境Python不存在: {venv_python}")
        print("请确保虚拟环境已正确创建")
        return 1
    
    # 启动应用
    try:
        gui_script = os.path.join(os.path.dirname(__file__), "email_gui_qt6.py")
        
        print(f"📧 使用Python: {venv_python}")
        print(f"📧 启动脚本: {gui_script}")
        print("-" * 50)
        
        # 使用虚拟环境的Python启动GUI
        result = subprocess.run([venv_python, gui_script], 
                              cwd=os.path.dirname(__file__))
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        return 0
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

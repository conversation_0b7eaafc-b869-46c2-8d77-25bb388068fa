@echo off
chcp 65001 >nul
title 临时邮箱工具 - PyQt6现代化界面

echo.
echo ================================================
echo 🚀 临时邮箱工具 - PyQt6现代化界面
echo ================================================
echo.

cd /d "%~dp0"

REM 检查虚拟环境
set VENV_PYTHON="%~dp0..\.venv\Scripts\python.exe"
if not exist %VENV_PYTHON% (
    echo ❌ 虚拟环境不存在，请先创建虚拟环境
    echo.
    echo 创建虚拟环境的命令：
    echo python -m venv .venv
    echo .venv\Scripts\activate
    echo pip install -r email\requirements.txt
    echo.
    pause
    exit /b 1
)

echo ✅ 找到虚拟环境Python: %VENV_PYTHON%
echo.

REM 启动应用
echo 🎯 启动PyQt6界面...
echo.
%VENV_PYTHON% email_gui_qt6.py

if errorlevel 1 (
    echo.
    echo ❌ 应用启动失败
    echo.
    echo 可能的解决方案：
    echo 1. 检查依赖包是否安装完整
    echo 2. 尝试重新安装PyQt6: pip install PyQt6
    echo 3. 检查Python版本是否兼容
    echo.
    pause
)

echo.
echo 👋 应用已退出
pause

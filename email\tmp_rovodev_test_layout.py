#!/usr/bin/env python3
"""
测试布局优化效果的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from email_gui_qt6 import ModernEmailApp

def test_layout():
    """测试布局优化"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("临时邮箱工具 - 布局测试")
    app.setApplicationVersion("2.0")
    
    # 创建主窗口
    window = ModernEmailApp()
    window.show()
    
    print("布局优化完成！主要改进：")
    print("1. 邮箱管理表格：")
    print("   - Token和Cookies列改为自适应拉伸，可完整显示长文本")
    print("   - 邮箱地址列宽度增加到220px")
    print("   - 操作列宽度增加到200px")
    print("   - 行高增加到100px，按钮更大更易点击")
    print("   - 按钮间距增加，不再拥挤")
    print("")
    print("2. 邮件查看表格：")
    print("   - 邮箱列固定宽度180px")
    print("   - 操作列固定宽度120px")
    print("   - 主题列自适应拉伸")
    print("   - 行高增加到80px")
    print("   - 启用文本换行和行高自适应")
    print("")
    print("3. 可复制文本组件：")
    print("   - 增加背景色和边框，更易识别")
    print("   - 复制按钮大小优化为24x24px")
    print("   - 组件间距优化")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    test_layout()
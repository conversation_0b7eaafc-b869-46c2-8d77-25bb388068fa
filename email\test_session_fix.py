#!/usr/bin/env python3
"""
测试session修复功能
验证cookies的序列化和恢复是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from singleuse_email_api import SingleUseEmailAPI
from database import DatabaseManager, run_async

def test_cookies_serialization():
    """测试cookies序列化功能"""
    print("=" * 50)
    print("测试cookies序列化功能")
    print("=" * 50)
    
    api = SingleUseEmailAPI()
    
    # 模拟一些cookies
    api.session.cookies.set('test_cookie1', 'value1')
    api.session.cookies.set('test_cookie2', 'value2')
    api.session.cookies.set('session_id', 'abc123')
    
    print(f"原始cookies: {dict(api.session.cookies)}")
    
    # 序列化
    cookies_str = api.serialize_cookies()
    print(f"序列化后: {cookies_str}")
    
    # 清除cookies
    api.session.cookies.clear()
    print(f"清除后: {dict(api.session.cookies)}")
    
    # 恢复cookies
    success = api.restore_cookies(cookies_str)
    print(f"恢复结果: {success}")
    print(f"恢复后cookies: {dict(api.session.cookies)}")
    
    return success

def test_session_restore():
    """测试完整session恢复功能"""
    print("\n" + "=" * 50)
    print("测试完整session恢复功能")
    print("=" * 50)
    
    api = SingleUseEmailAPI()
    
    # 模拟session状态
    api.csrf_token = "test_token_123"
    api.session.cookies.set('laravel_session', 'session_value')
    api.session.cookies.set('XSRF-TOKEN', 'xsrf_value')
    
    # 获取session信息
    token, cookies = api.get_session_info()
    print(f"获取到的token: {token}")
    print(f"获取到的cookies长度: {len(cookies) if cookies else 0}")
    
    # 创建新的API实例来模拟恢复
    api2 = SingleUseEmailAPI()
    print(f"新实例初始token: {api2.csrf_token}")
    print(f"新实例初始cookies: {dict(api2.session.cookies)}")
    
    # 恢复session
    success = api2.restore_session(token, cookies)
    print(f"恢复结果: {success}")
    print(f"恢复后token: {api2.csrf_token}")
    print(f"恢复后cookies: {dict(api2.session.cookies)}")
    
    return success

def test_database_migration():
    """测试数据库迁移功能"""
    print("\n" + "=" * 50)
    print("测试数据库迁移功能")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    
    # 初始化数据库
    success = run_async(db_manager.init_database())
    print(f"数据库初始化: {'成功' if success else '失败'}")
    
    # 测试添加邮箱（包含cookies）
    email_id = run_async(db_manager.add_email(
        "<EMAIL>", 
        "test_token", 
        "test_cookies_data"
    ))
    print(f"添加邮箱: {'成功' if email_id else '失败'} (ID: {email_id})")
    
    if email_id:
        # 获取邮箱信息
        emails = run_async(db_manager.get_all_emails())
        for email in emails:
            if email['id'] == email_id:
                print(f"邮箱信息: {email['email_name']}")
                print(f"Token: {email['token']}")
                print(f"Cookies: {email['cookies']}")
                break
    
    return success

if __name__ == "__main__":
    print("开始测试session修复功能...")
    
    # 测试cookies序列化
    test1 = test_cookies_serialization()
    
    # 测试session恢复
    test2 = test_session_restore()
    
    # 测试数据库迁移
    test3 = test_database_migration()
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"Cookies序列化: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"Session恢复: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"数据库迁移: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 所有测试通过！session修复功能已就绪")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能")

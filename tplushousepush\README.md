# 房源推送脚本

这个Python脚本模拟了PHP代码的房源推送功能，用于将房源信息推送到指定的API接口。

## 功能特性

- 模拟PHP的cURL请求功能
- 支持房源数据的JSON格式推送
- 完整的日志记录功能
- 支持自定义电话号码和推送URL
- 包含完整的房源数据结构（基于实际日志数据）

## 文件结构

```
tplushousepush/
├── p.py                 # 主要的房源推送脚本
├── example_usage.py     # 使用示例
├── requirements.txt     # Python依赖包
└── README.md           # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 基本使用

```python
from p import HousePushService

# 创建推送服务
service = HousePushService()

# 推送房源信息
result = service.push_house_to_site("13175004384")

print(f"推送结果: HTTP {result['httpCode']}")
print(f"响应内容: {result['content']}")
```

### 2. 直接运行主脚本

```bash
python p.py
```

### 3. 运行示例代码

```bash
python example_usage.py
```

## 主要类和方法

### HousePushService 类

#### 初始化参数
- `base_url`: 推送站点的基础URL（默认: "https://yf.benew.cn"）
- `log_level`: 日志级别（默认: logging.INFO）

#### 主要方法

- `push_house_to_site(phone)`: 推送房源信息到站点
- `create_house_data()`: 创建完整房源数据结构（包含29张图片）
- `my_curl(url, data, method)`: 模拟PHP的cURL功能

## 日志功能

脚本会自动生成日志文件 `house_push.log`，记录：
- 推送链接和房源信息
- 服务器返回结果
- 错误信息

## 房源数据结构

脚本包含完整的房源数据结构，包括：
- 基本房源信息（面积、价格、房型等）
- 房源详细信息（装修、朝向、配套设施等）
- 图片和视频信息
- 地理位置信息
- 联系方式

## 注意事项

1. 确保网络连接正常
2. 推送URL需要可访问
3. 日志文件会记录详细的推送过程
4. SSL证书验证默认关闭（模拟PHP行为）

## 错误处理

脚本包含完整的错误处理机制：
- 网络请求异常处理
- JSON解析异常处理
- 详细的错误日志记录

#!/usr/bin/env python3
"""
PyQt6界面启动脚本
用于测试和启动现代化的PyQt6界面
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import PyQt6
        print("✅ PyQt6 已安装")
    except ImportError:
        missing_packages.append("PyQt6")
    
    try:
        import aiosqlite
        print("✅ aiosqlite 已安装")
    except ImportError:
        missing_packages.append("aiosqlite")
    
    try:
        import requests
        print("✅ requests 已安装")
    except ImportError:
        missing_packages.append("requests")
    
    try:
        import pyperclip
        print("✅ pyperclip 已安装")
    except ImportError:
        missing_packages.append("pyperclip")
    
    if missing_packages:
        print(f"\n❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装：")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("\n✅ 所有依赖包已安装")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("临时邮箱工具 - PyQt6现代化界面")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    print("\n🚀 启动应用...")
    
    try:
        from email_gui_qt6 import main as qt6_main
        qt6_main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

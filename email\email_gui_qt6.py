"""
现代化临时邮箱GUI应用 - PyQt6版本
采用现代设计风格，支持邮箱管理、邮件监控、数据库操作等功能
"""

import sys
import os
import threading
import time
import asyncio
from datetime import datetime
from typing import Dict, List, Optional
import pyperclip

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTabWidget, QTableWidget, QTableWidgetItem, QTextEdit, QLineEdit,
    QPushButton, QLabel, QStatusBar, QSplitter, QHeaderView, QMessageBox,
    QProgressBar, QFrame, QScrollArea, QGroupBox, QCheckBox, QSpacerItem,
    QSizePolicy, QDialog, QDialogButtonBox, QFormLayout, QComboBox
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSize, QPropertyAnimation, 
    QEasingCurve, QRect, pyqtSlot
)
from PyQt6.QtGui import (
    QFont, QIcon, QPalette, QColor, QPixmap, QPainter, QBrush, 
    QLinearGradient, QAction
)

# 导入现有模块
from singleuse_email_api import SingleUseEmailAPI
from database import DatabaseManager, run_async


class ModernButton(QPushButton):
    """现代化按钮组件"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setMinimumHeight(40)
        self.setFont(QFont("Segoe UI", 10))
        self.setCursor(Qt.CursorShape.PointingHandCursor)


class CopyButton(QPushButton):
    """一键复制按钮组件"""

    def __init__(self, text_to_copy="", tooltip="复制", parent=None):
        super().__init__("📋", parent)
        self.text_to_copy = text_to_copy
        self.setToolTip(tooltip)
        self.setMinimumSize(20, 20)
        self.setMaximumSize(20, 20)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #f1f5f9);
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                font-size: 11px;
                font-weight: 500;
                padding: 4px;
                color: #64748b;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #eff6ff, stop:1 #dbeafe);
                border-color: #60a5fa;
                color: #3b82f6;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dbeafe, stop:1 #bfdbfe);
                border-color: #3b82f6;
                color: #1e40af;
            }
        """)
        self.clicked.connect(self.copy_text)

    def copy_text(self):
        """复制文本到剪贴板"""
        try:
            if self.text_to_copy:
                pyperclip.copy(self.text_to_copy)
                # 临时改变按钮文本显示复制成功
                original_text = self.text()
                self.setText("✓")
                self.setStyleSheet(self.styleSheet() + """
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #f0fdf4, stop:1 #dcfce7);
                        border-color: #22c55e;
                        color: #16a34a;
                    }
                """)
                # 1秒后恢复原状
                QTimer.singleShot(1000, lambda: self.reset_button(original_text))
        except Exception as e:
            print(f"复制失败: {e}")

    def reset_button(self, original_text):
        """重置按钮状态"""
        self.setText(original_text)
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #f1f5f9);
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                font-size: 11px;
                font-weight: 500;
                padding: 4px;
                color: #64748b;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #eff6ff, stop:1 #dbeafe);
                border-color: #60a5fa;
                color: #3b82f6;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dbeafe, stop:1 #bfdbfe);
                border-color: #3b82f6;
                color: #1e40af;
            }
        """)

    def update_text(self, new_text):
        """更新要复制的文本"""
        self.text_to_copy = new_text


class CopyableTextWidget(QWidget):
    """可复制的文本显示组件"""

    def __init__(self, text="", copy_tooltip="复制", parent=None):
        super().__init__(parent)
        self.setup_ui(text, copy_tooltip)

    def setup_ui(self, text, copy_tooltip):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(8)

        # 文本标签
        self.text_label = QLabel(text)
        self.text_label.setWordWrap(True)
        self.text_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        self.text_label.setMinimumHeight(24)
        self.text_label.setStyleSheet("""
            QLabel {
                padding: 4px 8px;
                background: rgba(248, 250, 252, 0.8);
                border: 1px solid rgba(226, 232, 240, 0.6);
                border-radius: 6px;
                font-size: 12px;
                color: #334155;
                line-height: 1.4;
            }
        """)
        layout.addWidget(self.text_label, 1)

        # 复制按钮
        self.copy_btn = CopyButton(text, copy_tooltip)
        self.copy_btn.setMinimumSize(24, 24)
        self.copy_btn.setMaximumSize(24, 24)
        layout.addWidget(self.copy_btn)

    def set_text(self, text):
        """设置文本"""
        self.text_label.setText(text)
        self.copy_btn.update_text(text)

    def get_text(self):
        """获取文本"""
        return self.text_label.text()


class EmailCard(QFrame):
    """邮箱卡片组件"""
    
    def __init__(self, email_data: Dict, parent=None):
        super().__init__(parent)
        self.email_data = email_data
        self.setup_ui()
    
    def setup_ui(self):
        """设置卡片UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setMinimumHeight(80)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # 邮箱地址
        email_label = QLabel(self.email_data.get('email_name', ''))
        email_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        layout.addWidget(email_label)
        
        # 状态信息
        status_layout = QHBoxLayout()
        
        # 状态标签
        if self.email_data.get('is_active'):
            status_label = QLabel("🟢 活跃")
        else:
            status_label = QLabel("🔴 未激活")
        
        if self.email_data.get('is_main_account'):
            status_label.setText(status_label.text() + " | 主账号")
        
        if self.email_data.get('is_team_account'):
            status_label.setText(status_label.text() + " | 团队账号")
        
        status_layout.addWidget(status_label)
        status_layout.addStretch()
        
        # 操作按钮
        monitor_btn = ModernButton("开始监控")
        monitor_btn.setMaximumWidth(100)
        status_layout.addWidget(monitor_btn)
        
        layout.addLayout(status_layout)


class EmailWorkerThread(QThread):
    """邮箱获取工作线程"""

    email_generated = pyqtSignal(str, str, str)  # 邮箱地址, token, cookies
    error_occurred = pyqtSignal(str)  # 错误信息

    def run(self):
        """获取邮箱"""
        try:
            api = SingleUseEmailAPI()
            email, messages, token, cookies = api.get_email_with_session()

            if email and token and cookies:
                self.email_generated.emit(email, token, cookies)
            else:
                self.error_occurred.emit("无法获取邮箱地址或session信息")

        except Exception as e:
            self.error_occurred.emit(str(e))



class EmailMonitorThread(QThread):
    """邮件监控线程"""

    new_email_received = pyqtSignal(dict, str)  # 邮件数据, 邮箱地址
    status_updated = pyqtSignal(str)  # 状态更新
    error_occurred = pyqtSignal(str)  # 错误信息

    def __init__(self, email_address: str, api_service: SingleUseEmailAPI, email_id: int = None):
        super().__init__()
        self.email_address = email_address
        self.api_service = api_service
        self.email_id = email_id
        self.monitoring = False
        self.received_emails = set()

    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.start()

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.quit()
        self.wait()

    def run(self):
        """监控线程主循环"""
        print(f"🚀 开始监控邮箱: {self.email_address}")

        while self.monitoring:
            try:
                emails = self.api_service.get_messages()

                new_emails = []
                for email in emails:
                    email_id = email.get('id', f"{email.get('from', '')}_{email.get('subject', '')}_{email.get('receivedAt', '')}")
                    if email_id not in self.received_emails:
                        self.received_emails.add(email_id)
                        new_emails.append(email)

                for email in new_emails:
                    self.new_email_received.emit(email, self.email_address)

                # 等待10秒
                for _ in range(100):
                    if not self.monitoring:
                        print(f"🛑 监控标志已设为False，退出等待循环: {self.email_address}")
                        break
                    self.msleep(100)  # 100ms

            except Exception as e:
                error_msg = f"监控邮件时发生错误: {str(e)}"
                print(f"❌ 监控线程异常: {error_msg}")

                # 如果是token相关错误，停止监控
                if "419" in str(e) or "token" in str(e).lower() or "proxy reauthentication" in str(e):
                    print(f"❌ 邮箱 {self.email_address} token失效，设置monitoring=False")
                    self.monitoring = False
                    self.error_occurred.emit(f"Token失效，监控已停止: {error_msg}")
                    print(f"🛑 即将退出监控循环: {self.email_address}")
                    break
                else:
                    self.error_occurred.emit(error_msg)
                    print(f"⏳ 非token错误，等待10秒后重试: {self.email_address}")
                    self.msleep(10000)  # 等待10秒后重试

        print(f"✅ 监控线程已退出: {self.email_address}")


class DatabaseInitThread(QThread):
    """数据库初始化线程"""
    
    init_completed = pyqtSignal(bool)  # 初始化完成信号
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    def run(self):
        """执行数据库初始化"""
        try:
            # 检查表是否存在
            tables_exist = run_async(self.db_manager.check_tables_exist())
            
            if not tables_exist:
                # 创建表
                success = run_async(self.db_manager.init_database())
                self.init_completed.emit(success)
            else:
                self.init_completed.emit(True)
                
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            self.init_completed.emit(False)


class EmailManagementWidget(QWidget):
    """邮箱管理页面"""

    # 定义信号
    refresh_email_messages_signal = pyqtSignal()

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.monitor_threads = {}  # 存储监控线程
        self.setup_ui()
        self.load_emails()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(24)
        layout.setContentsMargins(32, 32, 32, 32)
        
        # 顶部操作栏
        top_layout = QHBoxLayout()
        top_layout.setSpacing(16)

        # 获取新邮箱按钮（主要操作）
        self.get_email_btn = QPushButton("📧 获取新邮箱")
        self.get_email_btn.setMinimumHeight(48)
        self.get_email_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40a9ff, stop:1 #1890ff);
                color: white;
                border: 1px solid #1890ff;
                padding: 12px 32px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                min-width: 160px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #69c0ff, stop:1 #40a9ff);
                border-color: #40a9ff;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1890ff, stop:1 #096dd9);
                border-color: #096dd9;
            }
            QPushButton:disabled {
                background: #f5f5f5;
                color: #bfbfbf;
                border-color: #d9d9d9;
            }
        """)
        self.get_email_btn.clicked.connect(self.get_new_email)
        top_layout.addWidget(self.get_email_btn)

        # 刷新按钮（次要操作）
        self.refresh_btn = QPushButton("🔄 刷新列表")
        self.refresh_btn.setMinimumHeight(48)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #1890ff;
                border: 1px solid #1890ff;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 500;
                min-width: 120px;
            }
            QPushButton:hover {
                background: #f0f9ff;
                color: #40a9ff;
                border-color: #40a9ff;
            }
            QPushButton:pressed {
                background: #e6f7ff;
                color: #096dd9;
                border-color: #096dd9;
            }
        """)
        self.refresh_btn.clicked.connect(self.load_emails)
        top_layout.addWidget(self.refresh_btn)

        top_layout.addStretch()

        layout.addLayout(top_layout)
        
        # 邮箱列表
        self.email_table = QTableWidget()
        self.email_table.setColumnCount(8)
        self.email_table.setHorizontalHeaderLabels([
            "邮箱地址", "Token", "Cookies", "创建时间", "状态", "类型", "最后使用", "操作"
        ])
        
        # 设置表格样式
        header = self.email_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 邮箱地址
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Token - 自适应拉伸
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # Cookies - 自适应拉伸
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 创建时间
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 状态
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 类型
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # 最后使用
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)  # 操作

        # 设置固定列宽
        header.resizeSection(0, 220)  # 邮箱地址 - 增加宽度
        header.resizeSection(7, 200)  # 操作 - 增加宽度以容纳按钮

        self.email_table.setAlternatingRowColors(True)
        self.email_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.email_table.verticalHeader().setDefaultSectionSize(100)  # 增加最小行高
        self.email_table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)  # 行高自适应
        self.email_table.verticalHeader().setVisible(False)  # 隐藏行号
        self.email_table.setWordWrap(True)  # 启用文本换行
        self.email_table.setTextElideMode(Qt.TextElideMode.ElideNone)  # 不省略文本，完整显示
        
        layout.addWidget(self.email_table)
    
    def load_emails(self):
        """加载邮箱列表"""
        try:
            emails = run_async(self.db_manager.get_all_emails())
            
            self.email_table.setRowCount(len(emails))
            
            for row, email in enumerate(emails):
                # 邮箱地址 - 使用可复制组件
                email_widget = CopyableTextWidget(email['email_name'], "复制邮箱地址")
                self.email_table.setCellWidget(row, 0, email_widget)

                # Token - 显示完整内容，使用可复制组件
                token = email.get('token', '') or "无"
                token_widget = CopyableTextWidget(token, "复制Token")
                self.email_table.setCellWidget(row, 1, token_widget)

                # Cookies - 显示完整内容，使用可复制组件
                cookies = email.get('cookies', '') or "无"
                cookies_widget = CopyableTextWidget(cookies, "复制Cookies")
                self.email_table.setCellWidget(row, 2, cookies_widget)

                # 创建时间
                created_time = email['created_time']
                if created_time:
                    created_time = datetime.fromisoformat(created_time).strftime("%Y-%m-%d %H:%M")
                self.email_table.setItem(row, 3, QTableWidgetItem(created_time or ""))

                # 状态
                status = "🟢 活跃" if email['is_active'] else "🔴 未激活"
                self.email_table.setItem(row, 4, QTableWidgetItem(status))

                # 类型
                types = []
                if email['is_main_account']:
                    types.append("主账号")
                if email['is_team_account']:
                    types.append("团队账号")
                type_text = " | ".join(types) if types else "普通"
                self.email_table.setItem(row, 5, QTableWidgetItem(type_text))

                # 最后使用时间
                last_used = email['last_used_time']
                if last_used:
                    last_used = datetime.fromisoformat(last_used).strftime("%Y-%m-%d %H:%M")
                self.email_table.setItem(row, 6, QTableWidgetItem(last_used or ""))
                
                # 操作按钮 - 水平布局
                btn_widget = QWidget()
                btn_layout = QHBoxLayout(btn_widget)
                btn_layout.setContentsMargins(8, 8, 8, 8)
                btn_layout.setSpacing(8)

                # 监控按钮
                monitor_btn = QPushButton("监控")
                monitor_btn.setToolTip("开始监控邮件")
                monitor_btn.setMinimumWidth(50)
                monitor_btn.setMinimumHeight(32)
                monitor_btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #4ade80, stop:1 #22c55e);
                        color: white;
                        border: 1px solid #22c55e;
                        border-radius: 4px;
                        font-size: 10px;
                        font-weight: 500;
                        padding: 2px 6px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #86efac, stop:1 #4ade80);
                        border-color: #4ade80;
                    }
                """)
                monitor_btn.clicked.connect(lambda: self.start_monitoring(email))
                btn_layout.addWidget(monitor_btn)

                # 编辑按钮
                edit_btn = QPushButton("编辑")
                edit_btn.setToolTip("编辑邮箱")
                edit_btn.setMinimumWidth(50)
                edit_btn.setMinimumHeight(32)
                edit_btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #fbbf24, stop:1 #f59e0b);
                        color: #1f2937;
                        border: 1px solid #f59e0b;
                        border-radius: 4px;
                        font-size: 10px;
                        font-weight: 500;
                        padding: 2px 6px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #fcd34d, stop:1 #fbbf24);
                        border-color: #fbbf24;
                    }
                """)
                edit_btn.clicked.connect(lambda: self.edit_email(email))
                btn_layout.addWidget(edit_btn)

                # 删除按钮
                delete_btn = QPushButton("删除")
                delete_btn.setToolTip("删除邮箱")
                delete_btn.setMinimumWidth(50)
                delete_btn.setMinimumHeight(32)
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #f87171, stop:1 #ef4444);
                        color: white;
                        border: 1px solid #ef4444;
                        border-radius: 4px;
                        font-size: 10px;
                        font-weight: 500;
                        padding: 2px 6px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #fca5a5, stop:1 #f87171);
                        border-color: #f87171;
                    }
                """)
                delete_btn.clicked.connect(lambda: self.delete_email(email))
                btn_layout.addWidget(delete_btn)
                self.email_table.setCellWidget(row, 7, btn_widget)
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载邮箱列表失败: {str(e)}")
    
    def get_new_email(self):
        """获取新邮箱"""
        # 创建工作线程
        self.email_worker = EmailWorkerThread()
        self.email_worker.email_generated.connect(self.on_email_generated)
        self.email_worker.error_occurred.connect(self.on_email_error)

        # 禁用按钮并开始工作
        self.get_email_btn.setEnabled(False)
        self.get_email_btn.setText("获取中...")
        self.email_worker.start()

    @pyqtSlot(str, str, str)
    def on_email_generated(self, email: str, token: str, cookies: str):
        """邮箱生成成功"""
        try:
            # 保存到数据库（包含token和cookies）
            email_id = run_async(self.db_manager.add_email(email, token, cookies))

            if email_id:
                # 复制到剪贴板
                try:
                    pyperclip.copy(email)
                except:
                    pass

                # 刷新列表
                self.load_emails()

                QMessageBox.information(self, "成功", f"邮箱地址已生成: {email}\n已自动复制到剪贴板\n已保存完整session信息")
            else:
                QMessageBox.warning(self, "警告", "邮箱地址获取成功，但保存到数据库失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存邮箱时发生错误: {str(e)}")
        finally:
            self.get_email_btn.setEnabled(True)
            self.get_email_btn.setText("📧 获取新邮箱")

    @pyqtSlot(str)
    def on_email_error(self, error_msg: str):
        """邮箱生成失败"""
        QMessageBox.critical(self, "错误", f"获取邮箱地址失败: {error_msg}")
        self.get_email_btn.setEnabled(True)
        self.get_email_btn.setText("📧 获取新邮箱")
    
    def start_monitoring(self, email_data: Dict):
        """开始监控邮箱"""
        email_address = email_data['email_name']
        
        if email_address in self.monitor_threads:
            QMessageBox.information(self, "提示", f"邮箱 {email_address} 已在监控中")
            return
        
        try:
            api = SingleUseEmailAPI()
            api.current_email = email_address

            # 获取保存的token和cookies
            token = email_data.get('token')
            cookies = email_data.get('cookies')

            # 检查是否有完整的session信息
            if not token or not cookies:
                raise Exception(f"邮箱缺少完整的session信息 - Token: {'有' if token else '无'}, Cookies: {'有' if cookies else '无'}")

            # 恢复完整的session状态
            if not api.restore_session(token, cookies):
                raise Exception("无法恢复session状态")

            print(f"✅ 邮箱 {email_address} session状态恢复成功")

            monitor_thread = EmailMonitorThread(email_address, api, email_data['id'])
            monitor_thread.new_email_received.connect(self.on_new_email)
            monitor_thread.error_occurred.connect(self.on_monitor_error)

            self.monitor_threads[email_address] = monitor_thread
            monitor_thread.start_monitoring()

            # 更新最后使用时间
            run_async(self.db_manager.update_email_last_used(email_data['id']))

            QMessageBox.information(self, "成功", f"已开始监控邮箱: {email_address}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动监控失败: {str(e)}")
    
    def edit_email(self, email_data: Dict):
        """编辑邮箱"""
        dialog = EmailEditDialog(email_data, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            updated_data = dialog.get_data()
            
            success = run_async(self.db_manager.update_email_status(
                email_data['id'],
                updated_data['is_active'],
                updated_data['is_main_account'],
                updated_data['is_team_account']
            ))
            
            if success:
                self.load_emails()
                QMessageBox.information(self, "成功", "邮箱信息已更新")
            else:
                QMessageBox.critical(self, "错误", "更新邮箱信息失败")
    
    def delete_email(self, email_data: Dict):
        """删除邮箱"""
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除邮箱 {email_data['email_name']} 吗？\n这将同时删除所有相关邮件。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success = run_async(self.db_manager.delete_email(email_data['id']))
            
            if success:
                self.load_emails()
                QMessageBox.information(self, "成功", "邮箱已删除")
            else:
                QMessageBox.critical(self, "错误", "删除邮箱失败")
    
    @pyqtSlot(dict, str)
    def on_new_email(self, email_data: Dict, email_address: str):
        """处理新邮件"""
        try:
            # 获取邮件信息
            sender = email_data.get('from', '未知发件人')
            subject = email_data.get('subject', '无主题')
            content = email_data.get('content', '')
            received_time = email_data.get('receivedAt', '')

            print(f"收到新邮件: {subject} - {email_address}")
            print(f"邮件详细信息: {email_data}")

            # 查找邮箱ID
            emails = run_async(self.db_manager.get_all_emails())
            email_id = None
            for email in emails:
                if email['email_name'] == email_address:
                    email_id = email['id']
                    break

            if email_id:
                # 保存邮件到数据库
                message_id = run_async(self.db_manager.add_email_message(
                    email_id=email_id,
                    email_name=email_address,
                    sender=sender,
                    subject=subject,
                    content=content,
                    received_time=received_time
                ))

                if message_id:
                    print(f"✅ 邮件已保存到数据库: ID {message_id}")
                    # 通知主窗口刷新邮件列表
                    self.refresh_email_messages_signal.emit()
                else:
                    print(f"⚠️ 邮件可能已存在，未重复保存")
            else:
                print(f"❌ 未找到邮箱 {email_address} 的ID，无法保存邮件")

        except Exception as e:
            print(f"❌ 保存邮件时发生错误: {str(e)}")
    
    @pyqtSlot(str)
    def on_monitor_error(self, error_msg: str):
        """处理监控错误"""
        print(f"监控错误: {error_msg}")

        # 如果是token相关错误，清理监控线程并提示用户
        if "token" in error_msg.lower() or "419" in error_msg or "Token失效" in error_msg:
            # 找到并移除对应的监控线程
            for email_address, thread in list(self.monitor_threads.items()):
                if not thread.monitoring:  # 如果线程已停止监控
                    print(f"🧹 清理已停止的监控线程: {email_address}")
                    thread.quit()
                    thread.wait()
                    del self.monitor_threads[email_address]

            QMessageBox.warning(self, "监控停止", f"邮箱监控因token失效而停止：\n{error_msg}")
            # 刷新界面以更新状态
            self.load_emails()


class EmailEditDialog(QDialog):
    """邮箱编辑对话框"""
    
    def __init__(self, email_data: Dict, parent=None):
        super().__init__(parent)
        self.email_data = email_data
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("编辑邮箱")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # 表单
        form_layout = QFormLayout()
        
        # 邮箱地址（只读）
        email_label = QLabel(self.email_data['email_name'])
        email_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        form_layout.addRow("邮箱地址:", email_label)
        
        # 是否激活
        self.active_checkbox = QCheckBox()
        self.active_checkbox.setChecked(self.email_data['is_active'])
        form_layout.addRow("是否激活:", self.active_checkbox)
        
        # 是否主账号
        self.main_checkbox = QCheckBox()
        self.main_checkbox.setChecked(self.email_data['is_main_account'])
        form_layout.addRow("主账号:", self.main_checkbox)
        
        # 是否团队账号
        self.team_checkbox = QCheckBox()
        self.team_checkbox.setChecked(self.email_data['is_team_account'])
        form_layout.addRow("团队账号:", self.team_checkbox)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def get_data(self) -> Dict:
        """获取编辑后的数据"""
        return {
            'is_active': self.active_checkbox.isChecked(),
            'is_main_account': self.main_checkbox.isChecked(),
            'is_team_account': self.team_checkbox.isChecked()
        }


class EmailMessagesWidget(QWidget):
    """邮件管理页面"""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_messages()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(24)
        layout.setContentsMargins(32, 32, 32, 32)

        # 顶部操作栏
        top_layout = QHBoxLayout()
        top_layout.setSpacing(16)

        # 刷新邮件按钮
        self.refresh_btn = QPushButton("🔄 刷新邮件")
        self.refresh_btn.setMinimumHeight(40)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #95de64, stop:1 #52c41a);
                color: white;
                border: 1px solid #52c41a;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #b7eb8f, stop:1 #73d13d);
                border-color: #73d13d;
            }
        """)
        self.refresh_btn.clicked.connect(self.load_messages)
        top_layout.addWidget(self.refresh_btn)

        # 清空邮件按钮
        self.clear_btn = QPushButton("🗑️ 清空所有邮件")
        self.clear_btn.setMinimumHeight(40)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff7875, stop:1 #ff4d4f);
                color: white;
                border: 1px solid #ff4d4f;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff9c99, stop:1 #ff7875);
                border-color: #ff7875;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_all_messages)
        top_layout.addWidget(self.clear_btn)

        top_layout.addStretch()

        # 邮箱筛选
        filter_label = QLabel("📧 筛选邮箱:")
        filter_label.setStyleSheet("font-weight: 500; color: #424242;")
        top_layout.addWidget(filter_label)

        self.email_filter = QComboBox()
        self.email_filter.addItem("所有邮箱", "")
        self.email_filter.setMinimumWidth(200)
        self.email_filter.currentTextChanged.connect(self.filter_messages)
        top_layout.addWidget(self.email_filter)

        layout.addLayout(top_layout)

        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 邮件列表
        self.message_table = QTableWidget()
        self.message_table.setColumnCount(5)
        self.message_table.setHorizontalHeaderLabels([
            "邮箱", "发件人", "主题", "接收时间", "操作"
        ])

        # 设置表格样式
        header = self.message_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 邮箱
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 发件人
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # 主题
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 接收时间
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # 操作
        
        # 设置固定列宽
        header.resizeSection(0, 180)  # 邮箱列
        header.resizeSection(4, 120)  # 操作列

        self.message_table.setAlternatingRowColors(True)
        self.message_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.message_table.verticalHeader().setDefaultSectionSize(80)  # 增加行高
        self.message_table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)  # 行高自适应
        self.message_table.verticalHeader().setVisible(False)  # 隐藏行号
        self.message_table.setWordWrap(True)  # 启用文本换行
        self.message_table.itemSelectionChanged.connect(self.on_message_selected)

        splitter.addWidget(self.message_table)

        # 邮件内容显示
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        content_layout.addWidget(QLabel("邮件内容:"))

        self.content_display = QTextEdit()
        self.content_display.setReadOnly(True)
        content_layout.addWidget(self.content_display)

        splitter.addWidget(content_widget)
        splitter.setSizes([400, 400])

        layout.addWidget(splitter)

        # 加载邮箱列表到筛选器
        self.load_email_filter()

    def load_email_filter(self):
        """加载邮箱筛选列表"""
        try:
            emails = run_async(self.db_manager.get_all_emails())

            self.email_filter.clear()
            self.email_filter.addItem("所有邮箱", "")

            for email in emails:
                self.email_filter.addItem(email['email_name'], email['email_name'])

        except Exception as e:
            print(f"加载邮箱筛选列表失败: {e}")

    def load_messages(self):
        """加载邮件列表"""
        try:
            messages = run_async(self.db_manager.get_email_messages())

            self.message_table.setRowCount(len(messages))

            for row, message in enumerate(messages):
                # 邮箱
                self.message_table.setItem(row, 0, QTableWidgetItem(message['email_name']))

                # 发件人
                sender = message['sender'] or "未知发件人"
                self.message_table.setItem(row, 1, QTableWidgetItem(sender))

                # 主题
                subject = message['subject'] or "无主题"
                self.message_table.setItem(row, 2, QTableWidgetItem(subject))

                # 接收时间
                received_time = message['received_time']
                if received_time:
                    try:
                        received_time = datetime.fromisoformat(received_time).strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        pass
                self.message_table.setItem(row, 3, QTableWidgetItem(received_time or ""))

                # 操作按钮
                btn_widget = QWidget()
                btn_layout = QHBoxLayout(btn_widget)
                btn_layout.setContentsMargins(10, 8, 10, 8)
                btn_layout.setSpacing(6)

                # 删除按钮
                delete_btn = QPushButton("删除")
                delete_btn.setToolTip("删除邮件")
                delete_btn.setMinimumWidth(60)
                delete_btn.setMinimumHeight(36)
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #ff7875, stop:1 #ff4d4f);
                        color: white;
                        border: 1px solid #ff4d4f;
                        border-radius: 6px;
                        font-size: 12px;
                        font-weight: 500;
                        padding: 4px 8px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #ff9c99, stop:1 #ff7875);
                        border-color: #ff7875;
                    }
                """)
                delete_btn.clicked.connect(lambda: self.delete_message(message))
                btn_layout.addWidget(delete_btn)

                btn_layout.addStretch()
                self.message_table.setCellWidget(row, 4, btn_widget)

                # 存储消息数据
                self.message_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, message)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载邮件列表失败: {str(e)}")

    def filter_messages(self):
        """筛选邮件"""
        filter_email = self.email_filter.currentData()

        for row in range(self.message_table.rowCount()):
            item = self.message_table.item(row, 0)
            if filter_email == "" or item.text() == filter_email:
                self.message_table.setRowHidden(row, False)
            else:
                self.message_table.setRowHidden(row, True)

    def on_message_selected(self):
        """邮件选中事件"""
        current_row = self.message_table.currentRow()
        if current_row >= 0:
            item = self.message_table.item(current_row, 0)
            if item:
                message_data = item.data(Qt.ItemDataRole.UserRole)
                if message_data:
                    content = message_data.get('content', '无内容')

                    # 格式化显示内容
                    formatted_content = f"""
发件人: {message_data.get('sender', '未知')}
主题: {message_data.get('subject', '无主题')}
接收时间: {message_data.get('received_time', '未知')}
邮箱: {message_data.get('email_name', '未知')}

内容:
{content}
"""
                    self.content_display.setPlainText(formatted_content)

    def delete_message(self, message_data: Dict):
        """删除邮件"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除这封邮件吗？\n主题: {message_data.get('subject', '无主题')}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            success = run_async(self.db_manager.delete_email_message(message_data['id']))

            if success:
                self.load_messages()
                QMessageBox.information(self, "成功", "邮件已删除")
            else:
                QMessageBox.critical(self, "错误", "删除邮件失败")

    def clear_all_messages(self):
        """清空所有邮件"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有邮件吗？此操作不可恢复。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                messages = run_async(self.db_manager.get_email_messages())
                for message in messages:
                    run_async(self.db_manager.delete_email_message(message['id']))

                self.load_messages()
                QMessageBox.information(self, "成功", "所有邮件已清空")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"清空邮件失败: {str(e)}")


class SettingsWidget(QWidget):
    """设置页面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(28)
        layout.setContentsMargins(32, 32, 32, 32)

        # 应用设置组
        app_group = QGroupBox("应用设置")
        app_layout = QFormLayout(app_group)

        # 监控间隔
        self.monitor_interval = QComboBox()
        self.monitor_interval.addItems(["5秒", "10秒", "30秒", "1分钟", "5分钟"])
        self.monitor_interval.setCurrentText("10秒")
        app_layout.addRow("监控间隔:", self.monitor_interval)

        # 自动复制
        self.auto_copy = QCheckBox("获取邮箱时自动复制到剪贴板")
        self.auto_copy.setChecked(True)
        app_layout.addRow(self.auto_copy)

        # 新邮件通知
        self.email_notification = QCheckBox("收到新邮件时显示通知")
        self.email_notification.setChecked(True)
        app_layout.addRow(self.email_notification)

        layout.addWidget(app_group)

        # 数据库设置组
        db_group = QGroupBox("数据库设置")
        db_layout = QVBoxLayout(db_group)

        # 数据库路径
        db_path_layout = QHBoxLayout()
        self.db_path_label = QLabel("数据库路径: email/db/email_database.db")
        db_path_layout.addWidget(self.db_path_label)

        backup_btn = QPushButton("💾 备份数据库")
        backup_btn.setMinimumHeight(40)
        backup_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffd666, stop:1 #faad14);
                color: #262626;
                border: 1px solid #faad14;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffe58f, stop:1 #ffc53d);
                border-color: #ffc53d;
            }
        """)
        backup_btn.clicked.connect(self.backup_database)
        db_path_layout.addWidget(backup_btn)

        db_layout.addLayout(db_path_layout)

        layout.addWidget(db_group)

        # 关于信息
        about_group = QGroupBox("关于")
        about_layout = QVBoxLayout(about_group)

        about_text = QLabel("""
临时邮箱工具 v2.0 - PyQt6版本

功能特性:
• 现代化PyQt6界面设计
• 邮箱管理和邮件监控
• SQLite数据库存储
• 多邮箱同时监控
• 邮件内容去重

技术栈:
• PyQt6 - 现代GUI框架
• SQLite - 轻量级数据库
• aiosqlite - 异步数据库操作
• requests - HTTP请求库
""")
        about_text.setWordWrap(True)
        about_layout.addWidget(about_text)

        layout.addWidget(about_group)

        layout.addStretch()

    def backup_database(self):
        """备份数据库"""
        QMessageBox.information(self, "提示", "数据库备份功能待实现")


class ModernEmailApp(QMainWindow):
    """现代化邮箱应用主窗口"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.init_database()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("临时邮箱工具 - 现代化版本")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(0)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 邮箱管理页面
        self.email_management = EmailManagementWidget(self.db_manager)
        self.tab_widget.addTab(self.email_management, "📧 邮箱管理")

        # 邮件查看页面
        self.email_messages = EmailMessagesWidget(self.db_manager)
        self.tab_widget.addTab(self.email_messages, "📬 邮件查看")

        # 连接信号
        self.email_management.refresh_email_messages_signal.connect(self.email_messages.load_messages)

        # 设置页面
        self.settings = SettingsWidget()
        self.tab_widget.addTab(self.settings, "⚙️ 设置")

        layout.addWidget(self.tab_widget)

        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")

        # 应用样式
        self.apply_modern_style()

    def apply_modern_style(self):
        """应用现代化Material Design + Ant Design风格样式"""
        style = """
        /* 主窗口 - 现代化渐变背景 */
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #f8fafc, stop:0.3 #f1f5f9, stop:0.7 #e2e8f0, stop:1 #f8fafc);
            color: #1e293b;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        }

        /* 现代化标签页 - 卡片风格 */
        QTabWidget::pane {
            border: 1px solid #e2e8f0;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            margin-top: 8px;
        }

        QTabWidget::tab-bar {
            alignment: left;
            background: transparent;
        }

        QTabBar::tab {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8fafc);
            color: #64748b;
            border: 1px solid #e2e8f0;
            padding: 18px 28px;
            margin-right: 4px;
            margin-bottom: 8px;
            border-radius: 12px 12px 0 0;
            font-size: 15px;
            font-weight: 500;
            min-width: 120px;
        }

        QTabBar::tab:selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8fafc);
            color: #3b82f6;
            border: 2px solid #3b82f6;
            border-bottom: 3px solid #3b82f6;
            font-weight: 600;
        }

        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f1f5f9, stop:1 #e2e8f0);
            color: #475569;
            border-color: #94a3b8;
        }

        /* 现代化按钮 - Material Design + 微软Fluent风格 */
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #60a5fa, stop:0.5 #3b82f6, stop:1 #2563eb);
            color: white;
            border: 1px solid #3b82f6;
            padding: 12px 24px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            min-height: 40px;
            min-width: 100px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #93c5fd, stop:0.5 #60a5fa, stop:1 #3b82f6);
            border-color: #60a5fa;
        }

        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2563eb, stop:0.5 #1d4ed8, stop:1 #1e40af);
            border-color: #1d4ed8;
        }

        QPushButton:disabled {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f1f5f9, stop:1 #e2e8f0);
            color: #94a3b8;
            border-color: #e2e8f0;
        }

        /* 次要按钮 - 现代线框风格 */
        QPushButton[class="secondary"] {
            background: rgba(255, 255, 255, 0.9);
            color: #3b82f6;
            border: 2px solid #3b82f6;
        }

        QPushButton[class="secondary"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #eff6ff, stop:1 #dbeafe);
            color: #2563eb;
            border-color: #2563eb;
        }

        QPushButton[class="secondary"]:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #dbeafe, stop:1 #bfdbfe);
            color: #1d4ed8;
            border-color: #1d4ed8;
        }

        /* 危险按钮 - 现代红色系 */
        QPushButton[class="danger"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f87171, stop:0.5 #ef4444, stop:1 #dc2626);
            border: 1px solid #ef4444;
        }

        QPushButton[class="danger"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #fca5a5, stop:0.5 #f87171, stop:1 #ef4444);
            border-color: #f87171;
        }

        QPushButton[class="danger"]:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b);
            border-color: #b91c1c;
        }

        /* 成功按钮 - 现代绿色系 */
        QPushButton[class="success"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4ade80, stop:0.5 #22c55e, stop:1 #16a34a);
            border: 1px solid #22c55e;
        }

        QPushButton[class="success"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #86efac, stop:0.5 #4ade80, stop:1 #22c55e);
            border-color: #4ade80;
        }

        /* 警告按钮 - 现代橙色系 */
        QPushButton[class="warning"] {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706);
            border: 1px solid #f59e0b;
            color: #1f2937;
        }

        QPushButton[class="warning"]:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #fcd34d, stop:0.5 #fbbf24, stop:1 #f59e0b);
            border-color: #fbbf24;
        }

        /* 现代化表格 - 高级卡片风格 */
        QTableWidget {
            gridline-color: rgba(226, 232, 240, 0.6);
            background: rgba(255, 255, 255, 0.95);
            alternate-background-color: rgba(248, 250, 252, 0.8);
            selection-background-color: rgba(59, 130, 246, 0.1);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            outline: none;
        }

        QTableWidget::item {
            padding: 16px 20px;
            border: none;
            border-bottom: 1px solid rgba(226, 232, 240, 0.4);
            font-size: 14px;
            color: #334155;
        }

        QTableWidget::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(59, 130, 246, 0.15), stop:1 rgba(147, 197, 253, 0.1));
            color: #1e40af;
            border-left: 3px solid #3b82f6;
        }

        QTableWidget::item:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(241, 245, 249, 0.9), stop:1 rgba(226, 232, 240, 0.6));
            color: #1e293b;
        }

        QHeaderView::section {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(248, 250, 252, 0.95), stop:1 rgba(241, 245, 249, 0.9));
            padding: 16px 20px;
            border: none;
            border-bottom: 2px solid rgba(59, 130, 246, 0.2);
            border-right: 1px solid rgba(226, 232, 240, 0.5);
            font-weight: 700;
            font-size: 13px;
            color: #475569;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        QHeaderView::section:first {
            border-top-left-radius: 12px;
        }

        QHeaderView::section:last {
            border-top-right-radius: 12px;
            border-right: none;
        }

        QHeaderView::section:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(226, 232, 240, 0.8), stop:1 rgba(203, 213, 225, 0.7));
            color: #1e293b;
        }

        /* 现代化输入框 - Material Design风格 */
        QTextEdit, QLineEdit {
            border: 2px solid rgba(226, 232, 240, 0.8);
            border-radius: 10px;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.95);
            font-size: 14px;
            selection-background-color: rgba(147, 197, 253, 0.3);
            color: #1e293b;
        }

        QTextEdit:focus, QLineEdit:focus {
            border-color: #3b82f6;
            outline: none;
            background: white;
        }

        QTextEdit:hover, QLineEdit:hover {
            border-color: #60a5fa;
        }

        /* 现代化下拉框 - 高级风格 */
        QComboBox {
            border: 2px solid rgba(226, 232, 240, 0.8);
            border-radius: 10px;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.95);
            font-size: 14px;
            min-width: 140px;
            min-height: 40px;
            color: #1e293b;
        }

        QComboBox:focus {
            border-color: #3b82f6;
        }

        QComboBox:hover {
            border-color: #60a5fa;
        }

        QComboBox::drop-down {
            border: none;
            width: 40px;
            background: transparent;
            border-radius: 0 8px 8px 0;
        }

        QComboBox::down-arrow {
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQuNSA2LjVMOSAxMUwxMy41IDYuNSIgc3Ryb2tlPSIjNjQ3NDhiIiBzdHJva2Utd2lkdGg9IjIuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            width: 18px;
            height: 18px;
        }

        QComboBox QAbstractItemView {
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.98);
            selection-background-color: rgba(59, 130, 246, 0.1);
            outline: none;
            padding: 8px;
        }

        QComboBox QAbstractItemView::item {
            padding: 12px 16px;
            border-radius: 6px;
            margin: 2px;
        }

        QComboBox QAbstractItemView::item:hover {
            background: rgba(59, 130, 246, 0.08);
            color: #1e40af;
        }

        /* 现代化分组框 - 高级卡片风格 */
        QGroupBox {
            font-weight: 700;
            font-size: 16px;
            color: #1e293b;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            margin-top: 20px;
            padding-top: 32px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.98), stop:1 rgba(248, 250, 252, 0.95));
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 24px;
            padding: 8px 20px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3b82f6, stop:1 #6366f1);
            border: 1px solid #3b82f6;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        /* 现代化复选框 - iOS风格 */
        QCheckBox {
            spacing: 12px;
            font-size: 14px;
            color: #1e293b;
            font-weight: 500;
        }

        QCheckBox::indicator {
            width: 20px;
            height: 20px;
        }

        QCheckBox::indicator:unchecked {
            border: 2px solid rgba(148, 163, 184, 0.8);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.95);
        }

        QCheckBox::indicator:checked {
            border: 2px solid #3b82f6;
            border-radius: 6px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #60a5fa, stop:1 #3b82f6);
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTEiIHZpZXdCb3g9IjAgMCAxNCAxMSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEuNSA1LjVMNS41IDkuNUwxMi41IDEuNSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }

        QCheckBox::indicator:hover {
            border-color: #60a5fa;
        }

        QCheckBox::indicator:unchecked:hover {
            background: rgba(241, 245, 249, 0.9);
            border-color: #94a3b8;
        }

        /* 现代化状态栏 - 玻璃效果 */
        QStatusBar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(248, 250, 252, 0.95), stop:1 rgba(241, 245, 249, 0.9));
            border-top: 1px solid rgba(226, 232, 240, 0.6);
            color: #64748b;
            font-size: 13px;
            font-weight: 500;
            padding: 8px 16px;
            backdrop-filter: blur(10px);
        }

        /* 现代化分割器 - 优雅风格 */
        QSplitter::handle {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(226, 232, 240, 0.4), stop:0.5 rgba(203, 213, 225, 0.6), stop:1 rgba(226, 232, 240, 0.4));
            width: 2px;
            height: 2px;
            border-radius: 1px;
        }

        QSplitter::handle:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #60a5fa, stop:0.5 #3b82f6, stop:1 #60a5fa);
            width: 3px;
            height: 3px;
            border-radius: 1px;
        }

        QSplitter::handle:pressed {
            background: #2563eb;
            width: 4px;
            height: 4px;
        }

        /* 现代化标签 - 优化字体 */
        QLabel {
            color: #334155;
            font-size: 14px;
            font-weight: 500;
        }

        /* 现代化对话框 - 玻璃拟态风格 */
        QDialog {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.98), stop:1 rgba(248, 250, 252, 0.95));
            border: 1px solid #e2e8f0;
            border-radius: 20px;
        }

        /* 现代化消息框 - 高级风格 */
        QMessageBox {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.98), stop:1 rgba(248, 250, 252, 0.95));
            border: 1px solid #e2e8f0;
            border-radius: 16px;
        }

        QMessageBox QPushButton {
            min-width: 100px;
            margin: 6px;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
        }

        /* 现代化滚动条 - macOS风格 */
        QScrollBar:vertical {
            background: rgba(248, 250, 252, 0.8);
            width: 12px;
            border-radius: 6px;
            margin: 2px;
        }

        QScrollBar::handle:vertical {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(148, 163, 184, 0.6), stop:1 rgba(203, 213, 225, 0.8));
            border-radius: 6px;
            min-height: 30px;
            margin: 2px;
        }

        QScrollBar::handle:vertical:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(100, 116, 139, 0.7), stop:1 rgba(148, 163, 184, 0.9));
        }

        QScrollBar::handle:vertical:pressed {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #64748b, stop:1 #94a3b8);
        }

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }

        QScrollBar:horizontal {
            background: rgba(248, 250, 252, 0.8);
            height: 12px;
            border-radius: 6px;
            margin: 2px;
        }

        QScrollBar::handle:horizontal {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(148, 163, 184, 0.6), stop:1 rgba(203, 213, 225, 0.8));
            border-radius: 6px;
            min-width: 30px;
            margin: 2px;
        }
        """

        self.setStyleSheet(style)

    def init_database(self):
        """初始化数据库"""
        self.status_bar.showMessage("正在初始化数据库...")

        self.db_init_thread = DatabaseInitThread(self.db_manager)
        self.db_init_thread.init_completed.connect(self.on_database_initialized)
        self.db_init_thread.start()

    @pyqtSlot(bool)
    def on_database_initialized(self, success: bool):
        """数据库初始化完成"""
        if success:
            self.status_bar.showMessage("数据库初始化成功 - 就绪")
            # 启用相关功能
            self.email_management.get_email_btn.setEnabled(True)
        else:
            self.status_bar.showMessage("数据库初始化失败")
            QMessageBox.critical(self, "错误", "数据库初始化失败，部分功能可能无法使用")

    def closeEvent(self, event):
        """关闭事件"""
        # 停止所有监控线程
        if hasattr(self.email_management, 'monitor_threads'):
            for thread in self.email_management.monitor_threads.values():
                thread.stop_monitoring()

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("临时邮箱工具")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("EmailTool")

    # 创建主窗口
    window = ModernEmailApp()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()

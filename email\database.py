"""
数据库管理模块
提供SQLite数据库操作功能，支持异步操作
"""

import sqlite3
import aiosqlite
import asyncio
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器，提供异步数据库操作"""
    
    def __init__(self, db_path: str = None):
        """初始化数据库管理器"""
        if db_path is None:
            # 默认数据库路径
            db_dir = os.path.join(os.path.dirname(__file__), 'db')
            os.makedirs(db_dir, exist_ok=True)
            db_path = os.path.join(db_dir, 'email_database.db')
        
        self.db_path = db_path
        self.lock = threading.Lock()
        logger.info(f"数据库路径: {self.db_path}")
    
    async def init_database(self) -> bool:
        """初始化数据库，创建表结构"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # 创建邮箱表
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS emails (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email_name VARCHAR(150) NOT NULL UNIQUE,
                        token VARCHAR(200),
                        cookies TEXT,
                        created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1,
                        is_main_account BOOLEAN DEFAULT 0,
                        is_team_account BOOLEAN DEFAULT 0,
                        last_used_time DATETIME
                    )
                ''')
                
                # 创建邮件内容表
                await db.execute('''
                    CREATE TABLE IF NOT EXISTS email_messages (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email_id INTEGER NOT NULL,
                        email_name VARCHAR(150) NOT NULL,
                        sender VARCHAR(255),
                        subject TEXT,
                        content TEXT,
                        received_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (email_id) REFERENCES emails (id) ON DELETE CASCADE,
                        UNIQUE(email_id, sender, subject, received_time)
                    )
                ''')
                
                # 检查并添加cookies字段（用于数据库迁移）
                await self._migrate_add_cookies_column(db)

                # 创建索引
                await db.execute('CREATE INDEX IF NOT EXISTS idx_emails_name ON emails(email_name)')
                await db.execute('CREATE INDEX IF NOT EXISTS idx_messages_email_id ON email_messages(email_id)')
                await db.execute('CREATE INDEX IF NOT EXISTS idx_messages_received_time ON email_messages(received_time)')

                await db.commit()
                logger.info("数据库表结构创建成功")
                return True

        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
            return False

    async def _migrate_add_cookies_column(self, db):
        """迁移：为emails表添加cookies字段"""
        try:
            # 检查cookies字段是否存在
            cursor = await db.execute("PRAGMA table_info(emails)")
            columns = await cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'cookies' not in column_names:
                logger.info("检测到旧版数据库，添加cookies字段...")
                await db.execute('ALTER TABLE emails ADD COLUMN cookies TEXT')
                logger.info("cookies字段添加成功")
            else:
                logger.info("cookies字段已存在，跳过迁移")

        except Exception as e:
            logger.warning(f"添加cookies字段时出现警告: {e}")
            # 不抛出异常，因为这可能是正常的情况
    
    async def check_tables_exist(self) -> bool:
        """检查数据库表是否存在"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute('''
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name IN ('emails', 'email_messages')
                ''')
                tables = await cursor.fetchall()
                return len(tables) == 2
        except Exception as e:
            logger.error(f"检查表存在性失败: {e}")
            return False
    
    async def add_email(self, email_name: str, token: str = None, cookies: str = None,
                       is_main_account: bool = False, is_team_account: bool = False) -> Optional[int]:
        """添加邮箱记录"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute('''
                    INSERT OR REPLACE INTO emails
                    (email_name, token, cookies, is_main_account, is_team_account, last_used_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (email_name, token, cookies, is_main_account, is_team_account, datetime.now()))

                await db.commit()
                email_id = cursor.lastrowid
                logger.info(f"添加邮箱成功: {email_name} (ID: {email_id})")
                return email_id

        except Exception as e:
            logger.error(f"添加邮箱失败: {e}")
            return None
    
    async def get_all_emails(self) -> List[Dict]:
        """获取所有邮箱记录"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute('''
                    SELECT e.id, e.email_name, e.token, e.cookies, e.created_time, e.is_active,
                           e.is_main_account, e.is_team_account, e.last_used_time,
                           COUNT(m.id) as email_count
                    FROM emails e
                    LEFT JOIN email_messages m ON e.id = m.email_id
                    GROUP BY e.id, e.email_name, e.token, e.cookies, e.created_time, e.is_active,
                             e.is_main_account, e.is_team_account, e.last_used_time
                    ORDER BY e.created_time DESC
                ''')
                rows = await cursor.fetchall()

                emails = []
                for row in rows:
                    # 格式化创建时间
                    created_at = row[4]
                    if created_at:
                        try:
                            # 尝试解析时间并格式化
                            if isinstance(created_at, str):
                                dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                            else:
                                dt = created_at
                            created_at = dt.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            # 如果解析失败，保持原样
                            pass
                    
                    emails.append({
                        'id': row[0],
                        'email_name': row[1],
                        'token': row[2],
                        'cookies': row[3],
                        'created_at': created_at,
                        'is_active': bool(row[5]),
                        'is_main_account': bool(row[6]),
                        'is_team_account': bool(row[7]),
                        'last_used_time': row[8],
                        'email_count': row[9]
                    })
                
                return emails
                
        except Exception as e:
            logger.error(f"获取邮箱列表失败: {e}")
            return []
    
    async def update_email_status(self, email_id: int, is_active: bool = None, 
                                 is_main_account: bool = None, is_team_account: bool = None) -> bool:
        """更新邮箱状态"""
        try:
            updates = []
            params = []
            
            if is_active is not None:
                updates.append("is_active = ?")
                params.append(is_active)
            
            if is_main_account is not None:
                updates.append("is_main_account = ?")
                params.append(is_main_account)
            
            if is_team_account is not None:
                updates.append("is_team_account = ?")
                params.append(is_team_account)
            
            if not updates:
                return True
            
            params.append(email_id)
            
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(f'''
                    UPDATE emails SET {', '.join(updates)} WHERE id = ?
                ''', params)
                await db.commit()
                
                logger.info(f"更新邮箱状态成功: ID {email_id}")
                return True
                
        except Exception as e:
            logger.error(f"更新邮箱状态失败: {e}")
            return False
    
    async def delete_email(self, email_id: int) -> bool:
        """删除邮箱（级联删除相关邮件）"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # 先删除相关邮件
                await db.execute('DELETE FROM email_messages WHERE email_id = ?', (email_id,))
                # 再删除邮箱
                await db.execute('DELETE FROM emails WHERE id = ?', (email_id,))
                await db.commit()
                
                logger.info(f"删除邮箱成功: ID {email_id}")
                return True
                
        except Exception as e:
            logger.error(f"删除邮箱失败: {e}")
            return False
    
    async def add_email_message(self, email_id: int, email_name: str, sender: str, 
                               subject: str, content: str, received_time: str = None) -> Optional[int]:
        """添加邮件消息"""
        try:
            if received_time is None:
                received_time = datetime.now().isoformat()
            
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute('''
                    INSERT OR IGNORE INTO email_messages 
                    (email_id, email_name, sender, subject, content, received_time)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (email_id, email_name, sender, subject, content, received_time))
                
                await db.commit()
                
                if cursor.rowcount > 0:
                    message_id = cursor.lastrowid
                    logger.info(f"添加邮件成功: {subject} (ID: {message_id})")
                    return message_id
                else:
                    logger.info(f"邮件已存在，跳过: {subject}")
                    return None
                
        except Exception as e:
            logger.error(f"添加邮件失败: {e}")
            return None
    
    async def get_email_messages(self, email_id: int = None) -> List[Dict]:
        """获取邮件消息列表"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                if email_id:
                    cursor = await db.execute('''
                        SELECT id, email_id, email_name, sender, subject, content, received_time
                        FROM email_messages WHERE email_id = ? ORDER BY received_time DESC
                    ''', (email_id,))
                else:
                    cursor = await db.execute('''
                        SELECT id, email_id, email_name, sender, subject, content, received_time
                        FROM email_messages ORDER BY received_time DESC
                    ''')
                
                rows = await cursor.fetchall()
                
                messages = []
                for row in rows:
                    messages.append({
                        'id': row[0],
                        'email_id': row[1],
                        'email_name': row[2],
                        'sender': row[3],
                        'subject': row[4],
                        'content': row[5],
                        'received_time': row[6]
                    })
                
                return messages
                
        except Exception as e:
            logger.error(f"获取邮件列表失败: {e}")
            return []
    
    async def delete_email_message(self, message_id: int) -> bool:
        """删除邮件消息"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('DELETE FROM email_messages WHERE id = ?', (message_id,))
                await db.commit()
                
                logger.info(f"删除邮件成功: ID {message_id}")
                return True
                
        except Exception as e:
            logger.error(f"删除邮件失败: {e}")
            return False
    
    async def update_email_last_used(self, email_id: int) -> bool:
        """更新邮箱最后使用时间"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    UPDATE emails SET last_used_time = ? WHERE id = ?
                ''', (datetime.now(), email_id))
                await db.commit()
                return True

        except Exception as e:
            logger.error(f"更新邮箱使用时间失败: {e}")
            return False

    async def update_email_token(self, email_id: int, token: str) -> bool:
        """更新邮箱token"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    UPDATE emails SET token = ? WHERE id = ?
                ''', (token, email_id))
                await db.commit()

                logger.info(f"更新邮箱token成功: ID {email_id}")
                return True

        except Exception as e:
            logger.error(f"更新邮箱token失败: {e}")
            return False

    async def update_email_cookies(self, email_id: int, cookies: str) -> bool:
        """更新邮箱cookies"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    UPDATE emails SET cookies = ? WHERE id = ?
                ''', (cookies, email_id))
                await db.commit()

                logger.info(f"更新邮箱cookies成功: ID {email_id}")
                return True

        except Exception as e:
            logger.error(f"更新邮箱cookies失败: {e}")
            return False

    async def update_email_session(self, email_id: int, token: str, cookies: str) -> bool:
        """更新邮箱session信息（token和cookies）"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    UPDATE emails SET token = ?, cookies = ? WHERE id = ?
                ''', (token, cookies, email_id))
                await db.commit()

                logger.info(f"更新邮箱session成功: ID {email_id}")
                return True

        except Exception as e:
            logger.error(f"更新邮箱session失败: {e}")
            return False

    async def save_message(self, email_address: str, message_data: dict) -> Optional[int]:
        """保存邮件消息（根据邮箱地址）"""
        try:
            # 首先根据邮箱地址查找email_id
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute(
                    'SELECT id FROM emails WHERE email_name = ?', 
                    (email_address,)
                )
                row = await cursor.fetchone()
                
                if not row:
                    logger.warning(f"未找到邮箱: {email_address}")
                    return None
                
                email_id = row[0]
                
                # 提取邮件信息
                sender = message_data.get('from', '') or message_data.get('sender', '')
                subject = message_data.get('subject', '无主题')
                content = message_data.get('content', '') or message_data.get('body', '')
                received_time = message_data.get('receivedAt') or message_data.get('received_time')
                
                # 如果没有接收时间，使用当前时间
                if not received_time:
                    received_time = datetime.now().isoformat()
                
                # 调用add_email_message方法
                return await self.add_email_message(
                    email_id=email_id,
                    email_name=email_address,
                    sender=sender,
                    subject=subject,
                    content=content,
                    received_time=received_time
                )
                
        except Exception as e:
            logger.error(f"保存邮件失败: {e}")
            return None

    async def save_email(self, email_address: str, token: str = None, cookies: str = None) -> bool:
        """保存邮箱（兼容方法）"""
        try:
            email_id = await self.add_email(email_address, token, cookies)
            return email_id is not None
        except Exception as e:
            logger.error(f"保存邮箱失败: {e}")
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()


def run_async(coro):
    """在新的事件循环中运行异步函数"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果当前线程已有运行的事件循环，创建新线程
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        else:
            return loop.run_until_complete(coro)
    except RuntimeError:
        # 没有事件循环，创建新的
        return asyncio.run(coro)


if __name__ == "__main__":
    # 测试数据库功能
    async def test_database():
        print("测试数据库功能...")
        
        # 初始化数据库
        success = await db_manager.init_database()
        print(f"数据库初始化: {'成功' if success else '失败'}")
        
        # 检查表是否存在
        exists = await db_manager.check_tables_exist()
        print(f"表存在检查: {'通过' if exists else '失败'}")
        
        # 添加测试邮箱
        email_id = await db_manager.add_email("<EMAIL>", "test_token")
        print(f"添加邮箱: {'成功' if email_id else '失败'}")
        
        # 获取邮箱列表
        emails = await db_manager.get_all_emails()
        print(f"邮箱列表: {len(emails)} 个")
        
        print("数据库测试完成")
    
    asyncio.run(test_database())

"""
临时邮箱GUI应用
支持两种方案：API协议模拟和Selenium无头浏览器
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import pyperclip
from datetime import datetime
from singleuse_email_api import SingleUseEmailAPI

class EmailGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("临时邮箱工具")
        self.root.geometry("800x600")
        
        # 当前使用的邮箱服务
        self.current_service = None
        self.current_email = None
        self.monitoring = False
        self.monitor_thread = None
        self.received_emails = set()  # 存储已接收邮件的ID，避免重复显示
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮
        self.get_email_btn = ttk.Button(control_frame, text="获取邮箱地址", command=self.get_email_address)
        self.get_email_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.monitor_btn = ttk.Button(control_frame, text="开始监控邮件", command=self.toggle_monitoring)
        self.monitor_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 当前邮箱显示
        self.email_label = ttk.Label(control_frame, text="当前邮箱: 未获取", foreground="blue")
        self.email_label.pack(side=tk.LEFT, padx=(20, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="详细日志", padding="5")
        log_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=20)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def get_email_address(self):
        """获取邮箱地址"""
        def worker():
            try:
                self.status_var.set("正在获取邮箱地址...")
                self.get_email_btn.config(state="disabled")
                
                self.log_message("使用API协议模拟方案获取邮箱地址...")
                
                if self.current_service:
                    del self.current_service
                self.current_service = SingleUseEmailAPI()
                email = self.current_service.get_email_address()
                
                if email:
                    self.current_email = email
                    self.email_label.config(text=f"当前邮箱: {email}")
                    self.log_message(f"邮箱地址已生成: {email}")
                    
                    # 复制到剪贴板
                    try:
                        pyperclip.copy(email)
                        self.log_message("邮箱地址已自动复制到剪贴板")
                    except Exception as e:
                        self.log_message(f"复制到剪贴板失败: {e}")
                    
                    self.status_var.set("邮箱地址获取成功")
                    self.monitor_btn.config(state="normal")
                else:
                    self.log_message("获取邮箱地址失败，请重试")
                    self.status_var.set("获取邮箱地址失败")
                    
            except Exception as e:
                self.log_message(f"获取邮箱地址时发生错误: {e}")
                self.status_var.set("发生错误")
            finally:
                self.get_email_btn.config(state="normal")
        
        thread = threading.Thread(target=worker, daemon=True)
        thread.start()
    
    def toggle_monitoring(self):
        """切换邮件监控状态"""
        if not self.monitoring:
            self.start_monitoring()
        else:
            self.stop_monitoring()
    
    def start_monitoring(self):
        """开始监控邮件"""
        if not self.current_email or not self.current_service:
            messagebox.showwarning("警告", "请先获取邮箱地址")
            return
        
        self.monitoring = True
        self.monitor_btn.config(text="停止监控邮件")
        self.get_email_btn.config(state="disabled")
        self.log_message("开始监控邮件，每10秒检查一次...")
        self.status_var.set("正在监控邮件...")
        
        def monitor_worker():
            while self.monitoring:
                try:
                    emails = self.current_service.get_messages()
                    
                    new_emails = []
                    for email in emails:
                        # 创建邮件唯一标识
                        email_id = email.get('id', f"{email.get('from', '')}_{email.get('subject', '')}_{email.get('receivedAt', '')}")
                        if email_id not in self.received_emails:
                            self.received_emails.add(email_id)
                            new_emails.append(email)
                    
                    if new_emails:
                        for email in new_emails:
                            sender = email.get('from', 'Unknown')
                            subject = email.get('subject', 'No Subject')
                            content = email.get('content', '')
                            
                            self.log_message(f"邮箱 {self.current_email} 收到新邮件")
                            self.log_message(f"发件人: {sender}")
                            self.log_message(f"主题: {subject}")
                            if content:
                                # 限制内容长度
                                preview = content[:200] + "..." if len(content) > 200 else content
                                self.log_message(f"内容预览: {preview}")
                            self.log_message("-" * 50)
                    
                    # 等待10秒
                    for i in range(100):  # 100 * 0.1 = 10秒
                        if not self.monitoring:
                            break
                        time.sleep(0.1)
                        
                except Exception as e:
                    self.log_message(f"监控邮件时发生错误: {e}")
                    time.sleep(10)
        
        self.monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控邮件"""
        self.monitoring = False
        self.monitor_btn.config(text="开始监控邮件")
        self.get_email_btn.config(state="normal")
        self.log_message("已停止监控邮件")
        self.status_var.set("监控已停止")
    
    def on_closing(self):
        """程序关闭时的清理工作"""
        self.stop_monitoring()
        if self.current_service and hasattr(self.current_service, 'close'):
            self.current_service.close()
        self.root.destroy()
    
    def run(self):
        """运行GUI应用"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("临时邮箱工具已启动")
        self.log_message("请点击'获取邮箱地址'")
        self.root.mainloop()

if __name__ == "__main__":
    app = EmailGUI()
    app.run()
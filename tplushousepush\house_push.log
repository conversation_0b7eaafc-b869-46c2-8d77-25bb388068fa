2025-07-29 11:14:40,359 - INFO - 推送链接https://yf.benew.cn/houseResource/saveHouseByLm,推送房源信息{"id": 9774, "trade_status": 9, "trade_type": 1, "total_floor": 29, "floor": 25, "loudong": "19号楼", "danyuan": "1单元", "mianji": 140, "fanghao": "2503", "shi": 4, "ting": 2, "wei": 2, "rent_price": 0, "danjia": 11642, "is_top": 1, "sale_price": 1630000, "zhuangxiu": "毛坯", "chaoxiang": "南北", "agent_id": 2029, "vr_url": "", "ctime": "2025-07-26 17:25:24", "utime": "2025-07-26 17:25:24", "label": "", "house_info": {"house_code": "", "house_structure": "平层", "building_type": "板楼", "building_structure": "钢混结构", "heating_type": "", "ownership": "", "property_certificate_period": "满二", "property_rights": "", "mortgage": "有抵押", "mortgage_price": 0, "elevator": "有电梯", "built_year": "", "elevator_desc": "1梯1户", "first_upload_at": "2025-07-26", "last_trade_at": "", "check_in_time": "", "check_in": "随时入住", "term": "", "see_time": "", "electricity": "民电", "water": "民水", "gas": "有", "term_type": "", "facilities": "", "parking": "有车位"}, "house_certificate": 0, "house_certificate_image": [], "house_follow_info": [], "community_name": "秦忆荣府北区", "lat": 30.056402, "lng": 119.914986, "pic": [{"id": 21102, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610695937942.jpg", "is_cover": 0}, {"id": 21103, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610703103624.jpg", "is_cover": 0}, {"id": 21104, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610710382027.jpg", "is_cover": 0}, {"id": 21105, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610716110396.jpg", "is_cover": 0}, {"id": 21106, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610723193924.jpg", "is_cover": 0}, {"id": 21107, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610730730293.jpg", "is_cover": 0}, {"id": 21108, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610736632632.jpg", "is_cover": 0}, {"id": 21109, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610744846931.jpg", "is_cover": 0}, {"id": 21110, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610753524420.jpg", "is_cover": 0}, {"id": 21111, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610761585325.jpg", "is_cover": 0}, {"id": 21112, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610772688484.jpg", "is_cover": 0}, {"id": 21113, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610779931725.jpg", "is_cover": 0}, {"id": 21121, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610805809515.jpg", "is_cover": 0}, {"id": 21122, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610813845875.jpg", "is_cover": 0}, {"id": 21123, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610822886818.jpg", "is_cover": 0}, {"id": 21124, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610830911803.jpg", "is_cover": 0}, {"id": 21125, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610838414886.jpg", "is_cover": 0}, {"id": 21126, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610857761772.jpg", "is_cover": 0}, {"id": 21127, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610867393927.jpg", "is_cover": 0}, {"id": 21128, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610880350922.jpg", "is_cover": 0}, {"id": 21129, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610889186535.jpg", "is_cover": 0}, {"id": 21130, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610897399382.jpg", "is_cover": 0}, {"id": 21131, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610905911945.jpg", "is_cover": 0}, {"id": 21145, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610916101365.jpg", "is_cover": 0}, {"id": 21146, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610927173454.jpg", "is_cover": 0}, {"id": 21147, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610936370395.jpg", "is_cover": 0}, {"id": 21148, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610949672374.jpg", "is_cover": 0}, {"id": 21149, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610958743648.jpg", "is_cover": 0}, {"id": 21150, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610966954899.jpg", "is_cover": 0}], "area": "富阳区", "region": "富春", "has_key": 0, "has_entrust": 0, "videos": [{"url": "https://img.tfcs.cn/1/1137/im/video/tmp/tmp_1753610977688644.mp4"}], "vrs": [], "tel": "13175004384"}
2025-07-29 11:14:40,850 - INFO - 推送房源信息返回结果{"content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>500</title>\r\n    <meta name=\"robots\" content=\"noindex,nofollow\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\r\n    <style>\r\n        /* Base */\r\n        body {\r\n            color: #333;\r\n            font: 14px Verdana, \"Helvetica Neue\", helvetica, Arial, 'Microsoft YaHei', sans-serif;\r\n            margin: 0;\r\n            padding: 0 20px 20px;\r\n            word-break: break-word;\r\n        }\r\n        h1{\r\n            margin: 10px 0 0;\r\n            font-size: 28px;\r\n            font-weight: 500;\r\n            line-height: 32px;\r\n        }\r\n        h2{\r\n            color: #4288ce;\r\n            font-weight: 400;\r\n            padding: 6px 0;\r\n            margin: 6px 0 0;\r\n            font-size: 18px;\r\n            border-bottom: 1px solid #eee;\r\n        }\r\n        h3.subheading {\r\n            color: #4288ce;\r\n            margin: 6px 0 0;\r\n            font-weight: 400;\r\n        }\r\n        h3{\r\n            margin: 12px;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n        }\r\n        abbr{\r\n            cursor: help;\r\n            text-decoration: underline;\r\n            text-decoration-style: dotted;\r\n        }\r\n        a{\r\n            color: #868686;\r\n            cursor: pointer;\r\n        }\r\n        a:hover{\r\n            text-decoration: underline;\r\n        }\r\n        .line-error{\r\n            background: #f8cbcb;\r\n        }\r\n\r\n        .echo table {\r\n            width: 100%;\r\n        }\r\n\r\n        .echo pre {\r\n            padding: 16px;\r\n            overflow: auto;\r\n            font-size: 85%;\r\n            line-height: 1.45;\r\n            background-color: #f7f7f7;\r\n            border: 0;\r\n            border-radius: 3px;\r\n            font-family: Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\r\n        }\r\n\r\n        .echo pre > pre {\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        /* Layout */\r\n        .col-md-3 {\r\n            width: 25%;\r\n        }\r\n        .col-md-9 {\r\n            width: 75%;\r\n        }\r\n        [class^=\"col-md-\"] {\r\n            float: left;\r\n        }\r\n        .clearfix {\r\n            clear:both;\r\n        }\r\n        @media only screen\r\n        and (min-device-width : 375px)\r\n        and (max-device-width : 667px) {\r\n            .col-md-3,\r\n            .col-md-9 {\r\n                width: 100%;\r\n            }\r\n        }\r\n        /* Exception Info */\r\n        .bg_exception{\r\n            background:url(\"../../static/skin/tfy/img/500bg.png\") no-repeat;\r\n            background-size:100% 100%;\r\n            text-align: center;\r\n            padding-top:55px;\r\n            height:500px;\r\n        }\r\n        \r\n        .bg_exception .look{\r\n          margin-top:75px;\r\n          color:#fb5050;\r\n        }\r\n        .bg_exception .look a {\r\n            color:#fb5050;\r\n            display:inline-block;\r\n            margin-right: 6px;\r\n            font-size: 14px;\r\n            text-decoration-line: none;\r\n         }\r\n        .bg_exception .look a:hover{\r\n            font-size: 15px;\r\n            font-weight:700;\r\n         }\r\n        .bg_exception .look span{\r\n            font-size: 14px;\r\n            margin-right: 5px;           \r\n        }\r\n        .bg_exception>div{\r\n            border-top:0px;\r\n        }\r\n\r\n        .exception {\r\n            margin-top: 20px;\r\n        }\r\n        .exception .message{\r\n            padding: 12px;\r\n            border: 1px solid #ddd;\r\n            border-bottom: 0 none;\r\n            line-height: 18px;\r\n            font-size:16px;\r\n            border-top-left-radius: 4px;\r\n            border-top-right-radius: 4px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n\r\n        .exception .code{\r\n            float: left;\r\n            text-align: center;\r\n            color: #fff;\r\n            margin-right: 12px;\r\n            padding: 16px;\r\n            border-radius: 4px;\r\n            background: #999;\r\n        }\r\n        .exception .source-code{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n\r\n            background: #f9f9f9;\r\n            overflow-x: auto;\r\n\r\n        }\r\n        .exception .source-code pre{\r\n            margin: 0;\r\n        }\r\n        .exception .source-code pre ol{\r\n            margin: 0;\r\n            color: #4288ce;\r\n            display: inline-block;\r\n            min-width: 100%;\r\n            box-sizing: border-box;\r\n            font-size:14px;\r\n            font-family: \"Century Gothic\",Consolas,\"Liberation Mono\",Courier,Verdana;\r\n            padding-left: 40px;\r\n        }\r\n        .exception .source-code pre li{\r\n            border-left: 1px solid #ddd;\r\n            height: 18px;\r\n            line-height: 18px;\r\n        }\r\n        .exception .source-code pre code{\r\n            color: #333;\r\n            height: 100%;\r\n            display: inline-block;\r\n            border-left: 1px solid #fff;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n            border-top: 0 none;\r\n            line-height: 16px;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace ol{\r\n            margin: 12px;\r\n        }\r\n        .exception .trace ol li{\r\n            padding: 2px 4px;\r\n        }\r\n        .exception div:last-child{\r\n            border-bottom-left-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        /* Exception Variables */\r\n        .exception-var table{\r\n            width: 100%;\r\n            margin: 12px 0;\r\n            box-sizing: border-box;\r\n            table-layout:fixed;\r\n            word-wrap:break-word;\r\n        }\r\n        .exception-var table caption{\r\n            text-align: left;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            padding: 6px 0;\r\n        }\r\n        .exception-var table caption small{\r\n            font-weight: 300;\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n            color: #ccc;\r\n        }\r\n        .exception-var table tbody{\r\n            font-size: 13px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,\"微软雅黑\";\r\n        }\r\n        .exception-var table td{\r\n            padding: 0 6px;\r\n            vertical-align: top;\r\n            word-break: break-all;\r\n        }\r\n        .exception-var table td:first-child{\r\n            width: 28%;\r\n            font-weight: bold;\r\n            white-space: nowrap;\r\n        }\r\n        .exception-var table td pre{\r\n            margin: 0;\r\n        }\r\n\r\n        /* Copyright Info */\r\n        .copyright{\r\n            margin-top: 24px;\r\n            padding: 12px 0;\r\n            border-top: 1px solid #eee;\r\n        }\r\n\r\n        /* SPAN elements with the classes below are added by prettyprint. */\r\n        pre.prettyprint .pln { color: #000 }  /* plain text */\r\n        pre.prettyprint .str { color: #080 }  /* string content */\r\n        pre.prettyprint .kwd { color: #008 }  /* a keyword */\r\n        pre.prettyprint .com { color: #800 }  /* a comment */\r\n        pre.prettyprint .typ { color: #606 }  /* a type name */\r\n        pre.prettyprint .lit { color: #066 }  /* a literal value */\r\n        /* punctuation, lisp open bracket, lisp close bracket */\r\n        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }\r\n        pre.prettyprint .tag { color: #008 }  /* a markup tag name */\r\n        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */\r\n        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */\r\n        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */\r\n        pre.prettyprint .fun { color: red }  /* a function name */\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"echo\">\r\n            </div>\r\n        <div class=\"exception bg_exception\">\r\n\r\n            <div class=\"info\"><h1>您的访问出错了啦。。</h1></div>\r\n        <div>\r\n            <p class=\"look\">\r\n                                <span>您可以查看更多:</span>\r\n                                <a href=\"/info/list-1.html\">住宅出售</a>\r\n                                <a href=\"/info/list-2.html\">住宅出租</a>\r\n                                <a href=\"//vip.html\">会员端口</a>\r\n                                <a href=\"//tool/\">房贷计算</a>\r\n                                <a href=\"/build/index.php?isvideo=video\">视频看房</a>\r\n                                <a href=\"/ask\">房产问答</a>\r\n                            </p>\r\n        </div>\r\n\r\n    </div>\r\n    \r\n    \r\n    \r\n    <div class=\"copyright\">\r\n        <a title=\"官方网站\" href=\"http://www.tengfangyun.com\"></a>\r\n    </div>\r\n    </body>\r\n</html>\r\n", "httpCode": 500}
2025-07-29 11:21:47,501 - INFO - 推送链接https://yf.benew.cn/houseResource/saveHouseByLm,推送房源信息{"id": 9774, "trade_status": 9, "trade_type": 1, "total_floor": 29, "floor": 25, "loudong": "19号楼", "danyuan": "1单元", "mianji": 140, "fanghao": "2503", "shi": 4, "ting": 2, "wei": 2, "rent_price": 0, "danjia": 11642, "is_top": 1, "sale_price": 1630000, "zhuangxiu": "毛坯", "chaoxiang": "南北", "agent_id": 2029, "vr_url": "", "ctime": "2025-07-26 17:25:24", "utime": "2025-07-26 17:25:24", "label": "", "house_info": {"house_code": "", "house_structure": "平层", "building_type": "板楼", "building_structure": "钢混结构", "heating_type": "", "ownership": "", "property_certificate_period": "满二", "property_rights": "", "mortgage": "有抵押", "mortgage_price": 0, "elevator": "有电梯", "built_year": "", "elevator_desc": "1梯1户", "first_upload_at": "2025-07-26", "last_trade_at": "", "check_in_time": "", "check_in": "随时入住", "term": "", "see_time": "", "electricity": "民电", "water": "民水", "gas": "有", "term_type": "", "facilities": "", "parking": "有车位"}, "house_certificate": 0, "house_certificate_image": [], "house_follow_info": [], "community_name": "秦忆荣府北区", "lat": 30.056402, "lng": 119.914986, "pic": [{"id": 21102, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610695937942.jpg", "is_cover": 0}, {"id": 21103, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610703103624.jpg", "is_cover": 0}, {"id": 21104, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610710382027.jpg", "is_cover": 0}, {"id": 21105, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610716110396.jpg", "is_cover": 0}, {"id": 21106, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610723193924.jpg", "is_cover": 0}, {"id": 21107, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610730730293.jpg", "is_cover": 0}, {"id": 21108, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610736632632.jpg", "is_cover": 0}, {"id": 21109, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610744846931.jpg", "is_cover": 0}, {"id": 21110, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610753524420.jpg", "is_cover": 0}, {"id": 21111, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610761585325.jpg", "is_cover": 0}, {"id": 21112, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610772688484.jpg", "is_cover": 0}, {"id": 21113, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610779931725.jpg", "is_cover": 0}, {"id": 21121, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610805809515.jpg", "is_cover": 0}, {"id": 21122, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610813845875.jpg", "is_cover": 0}, {"id": 21123, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610822886818.jpg", "is_cover": 0}, {"id": 21124, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610830911803.jpg", "is_cover": 0}, {"id": 21125, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610838414886.jpg", "is_cover": 0}, {"id": 21126, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610857761772.jpg", "is_cover": 0}, {"id": 21127, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610867393927.jpg", "is_cover": 0}, {"id": 21128, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610880350922.jpg", "is_cover": 0}, {"id": 21129, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610889186535.jpg", "is_cover": 0}, {"id": 21130, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610897399382.jpg", "is_cover": 0}, {"id": 21131, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610905911945.jpg", "is_cover": 0}, {"id": 21145, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610916101365.jpg", "is_cover": 0}, {"id": 21146, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610927173454.jpg", "is_cover": 0}, {"id": 21147, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610936370395.jpg", "is_cover": 0}, {"id": 21148, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610949672374.jpg", "is_cover": 0}, {"id": 21149, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610958743648.jpg", "is_cover": 0}, {"id": 21150, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610966954899.jpg", "is_cover": 0}], "area": "富阳区", "region": "富春", "has_key": 0, "has_entrust": 0, "videos": [{"url": "https://img.tfcs.cn/1/1137/im/video/tmp/tmp_1753610977688644.mp4"}], "vrs": [], "tel": "13175004384"}
2025-07-29 11:21:48,008 - INFO - 推送房源信息返回结果{"content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>500</title>\r\n    <meta name=\"robots\" content=\"noindex,nofollow\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\r\n    <style>\r\n        /* Base */\r\n        body {\r\n            color: #333;\r\n            font: 14px Verdana, \"Helvetica Neue\", helvetica, Arial, 'Microsoft YaHei', sans-serif;\r\n            margin: 0;\r\n            padding: 0 20px 20px;\r\n            word-break: break-word;\r\n        }\r\n        h1{\r\n            margin: 10px 0 0;\r\n            font-size: 28px;\r\n            font-weight: 500;\r\n            line-height: 32px;\r\n        }\r\n        h2{\r\n            color: #4288ce;\r\n            font-weight: 400;\r\n            padding: 6px 0;\r\n            margin: 6px 0 0;\r\n            font-size: 18px;\r\n            border-bottom: 1px solid #eee;\r\n        }\r\n        h3.subheading {\r\n            color: #4288ce;\r\n            margin: 6px 0 0;\r\n            font-weight: 400;\r\n        }\r\n        h3{\r\n            margin: 12px;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n        }\r\n        abbr{\r\n            cursor: help;\r\n            text-decoration: underline;\r\n            text-decoration-style: dotted;\r\n        }\r\n        a{\r\n            color: #868686;\r\n            cursor: pointer;\r\n        }\r\n        a:hover{\r\n            text-decoration: underline;\r\n        }\r\n        .line-error{\r\n            background: #f8cbcb;\r\n        }\r\n\r\n        .echo table {\r\n            width: 100%;\r\n        }\r\n\r\n        .echo pre {\r\n            padding: 16px;\r\n            overflow: auto;\r\n            font-size: 85%;\r\n            line-height: 1.45;\r\n            background-color: #f7f7f7;\r\n            border: 0;\r\n            border-radius: 3px;\r\n            font-family: Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\r\n        }\r\n\r\n        .echo pre > pre {\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        /* Layout */\r\n        .col-md-3 {\r\n            width: 25%;\r\n        }\r\n        .col-md-9 {\r\n            width: 75%;\r\n        }\r\n        [class^=\"col-md-\"] {\r\n            float: left;\r\n        }\r\n        .clearfix {\r\n            clear:both;\r\n        }\r\n        @media only screen\r\n        and (min-device-width : 375px)\r\n        and (max-device-width : 667px) {\r\n            .col-md-3,\r\n            .col-md-9 {\r\n                width: 100%;\r\n            }\r\n        }\r\n        /* Exception Info */\r\n        .bg_exception{\r\n            background:url(\"../../static/skin/tfy/img/500bg.png\") no-repeat;\r\n            background-size:100% 100%;\r\n            text-align: center;\r\n            padding-top:55px;\r\n            height:500px;\r\n        }\r\n        \r\n        .bg_exception .look{\r\n          margin-top:75px;\r\n          color:#fb5050;\r\n        }\r\n        .bg_exception .look a {\r\n            color:#fb5050;\r\n            display:inline-block;\r\n            margin-right: 6px;\r\n            font-size: 14px;\r\n            text-decoration-line: none;\r\n         }\r\n        .bg_exception .look a:hover{\r\n            font-size: 15px;\r\n            font-weight:700;\r\n         }\r\n        .bg_exception .look span{\r\n            font-size: 14px;\r\n            margin-right: 5px;           \r\n        }\r\n        .bg_exception>div{\r\n            border-top:0px;\r\n        }\r\n\r\n        .exception {\r\n            margin-top: 20px;\r\n        }\r\n        .exception .message{\r\n            padding: 12px;\r\n            border: 1px solid #ddd;\r\n            border-bottom: 0 none;\r\n            line-height: 18px;\r\n            font-size:16px;\r\n            border-top-left-radius: 4px;\r\n            border-top-right-radius: 4px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n\r\n        .exception .code{\r\n            float: left;\r\n            text-align: center;\r\n            color: #fff;\r\n            margin-right: 12px;\r\n            padding: 16px;\r\n            border-radius: 4px;\r\n            background: #999;\r\n        }\r\n        .exception .source-code{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n\r\n            background: #f9f9f9;\r\n            overflow-x: auto;\r\n\r\n        }\r\n        .exception .source-code pre{\r\n            margin: 0;\r\n        }\r\n        .exception .source-code pre ol{\r\n            margin: 0;\r\n            color: #4288ce;\r\n            display: inline-block;\r\n            min-width: 100%;\r\n            box-sizing: border-box;\r\n            font-size:14px;\r\n            font-family: \"Century Gothic\",Consolas,\"Liberation Mono\",Courier,Verdana;\r\n            padding-left: 56px;\r\n        }\r\n        .exception .source-code pre li{\r\n            border-left: 1px solid #ddd;\r\n            height: 18px;\r\n            line-height: 18px;\r\n        }\r\n        .exception .source-code pre code{\r\n            color: #333;\r\n            height: 100%;\r\n            display: inline-block;\r\n            border-left: 1px solid #fff;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n            border-top: 0 none;\r\n            line-height: 16px;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace ol{\r\n            margin: 12px;\r\n        }\r\n        .exception .trace ol li{\r\n            padding: 2px 4px;\r\n        }\r\n        .exception div:last-child{\r\n            border-bottom-left-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        /* Exception Variables */\r\n        .exception-var table{\r\n            width: 100%;\r\n            margin: 12px 0;\r\n            box-sizing: border-box;\r\n            table-layout:fixed;\r\n            word-wrap:break-word;\r\n        }\r\n        .exception-var table caption{\r\n            text-align: left;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            padding: 6px 0;\r\n        }\r\n        .exception-var table caption small{\r\n            font-weight: 300;\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n            color: #ccc;\r\n        }\r\n        .exception-var table tbody{\r\n            font-size: 13px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,\"微软雅黑\";\r\n        }\r\n        .exception-var table td{\r\n            padding: 0 6px;\r\n            vertical-align: top;\r\n            word-break: break-all;\r\n        }\r\n        .exception-var table td:first-child{\r\n            width: 28%;\r\n            font-weight: bold;\r\n            white-space: nowrap;\r\n        }\r\n        .exception-var table td pre{\r\n            margin: 0;\r\n        }\r\n\r\n        /* Copyright Info */\r\n        .copyright{\r\n            margin-top: 24px;\r\n            padding: 12px 0;\r\n            border-top: 1px solid #eee;\r\n        }\r\n\r\n        /* SPAN elements with the classes below are added by prettyprint. */\r\n        pre.prettyprint .pln { color: #000 }  /* plain text */\r\n        pre.prettyprint .str { color: #080 }  /* string content */\r\n        pre.prettyprint .kwd { color: #008 }  /* a keyword */\r\n        pre.prettyprint .com { color: #800 }  /* a comment */\r\n        pre.prettyprint .typ { color: #606 }  /* a type name */\r\n        pre.prettyprint .lit { color: #066 }  /* a literal value */\r\n        /* punctuation, lisp open bracket, lisp close bracket */\r\n        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }\r\n        pre.prettyprint .tag { color: #008 }  /* a markup tag name */\r\n        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */\r\n        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */\r\n        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */\r\n        pre.prettyprint .fun { color: red }  /* a function name */\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"echo\">\r\n            </div>\r\n        <div class=\"exception\">\r\n    <div class=\"message\">\r\n\r\n            <div class=\"info\">\r\n                <div>\r\n                    <h2>[0]&nbsp;<abbr title=\"think\\exception\\ThrowableError\">ThrowableError</abbr> in <a class=\"toggle\" title=\"/www/wwwroot/tfy_fuyangfan/application/core/service/HouseResourceService.php line 803\">HouseResourceService.php line 803</a></h2>\r\n                </div>\r\n                <div><h1>类型错误: Argument 1 passed to app\\core\\service\\HouseResourceService::saveHouseByLm() must be of the type array, null given, called in /www/wwwroot/tfy_fuyangfan/application/index/controller/HouseResource.php on line 287</h1></div>\r\n            </div>\r\n\r\n    </div>\r\n\t        <div class=\"source-code\">\r\n            <pre class=\"prettyprint lang-php\"><ol start=\"794\"><li class=\"line-794\"><code>    /**\r\n</code></li><li class=\"line-795\"><code>     * 保存联卖房源 \r\n</code></li><li class=\"line-796\"><code>     * @param array $param\r\n</code></li><li class=\"line-797\"><code>     * @return array\r\n</code></li><li class=\"line-798\"><code>     * @throws \\think\\Exception\r\n</code></li><li class=\"line-799\"><code>     * @throws \\think\\db\\exception\\DataNotFoundException\r\n</code></li><li class=\"line-800\"><code>     * @throws \\think\\db\\exception\\ModelNotFoundException\r\n</code></li><li class=\"line-801\"><code>     * @throws \\think\\exception\\DbException\r\n</code></li><li class=\"line-802\"><code>     */\r\n</code></li><li class=\"line-803\"><code>    public function saveHouseByLm(array $param){\r\n</code></li><li class=\"line-804\"><code>        $lmAgent=Db::name('lm_agent')-&gt;where('tel',$param['tel'])-&gt;find();\r\n</code></li><li class=\"line-805\"><code>        if(!$lmAgent) return ['code'=&gt;0,'msg'=&gt;'当前账号未绑定'];\r\n</code></li><li class=\"line-806\"><code>        $member=Db::name('member')-&gt;where('tel',$param['tel'])-&gt;find();\r\n</code></li><li class=\"line-807\"><code>        if(!$member) return ['code'=&gt;0,'msg'=&gt;'会员不存在'];\r\n</code></li><li class=\"line-808\"><code>        if($member['isfrozen'])  return ['code' =&gt; 0, 'msg' =&gt; '会员已封禁'];\r\n</code></li><li class=\"line-809\"><code>        if($member['levelid'] &lt; 2)  return ['code' =&gt; 0, 'msg' =&gt; '请开通经纪人'];\r\n</code></li><li class=\"line-810\"><code>        if($member['levelup_time'] &lt; time())  return ['code' =&gt; 0, 'msg' =&gt; '经纪人已过期'];\r\n</code></li><li class=\"line-811\"><code>        $info = Db::name('information')-&gt;field(&quot;id&quot;)-&gt;where('uid', $member['id'])-&gt;where('lm_id', $param['id'])-&gt;find();\r\n</code></li><li class=\"line-812\"><code>        $infoid = $info['id'] ?? 0;\r\n</code></li></ol></pre>\r\n        </div>\r\n\t        <div class=\"trace\">\r\n\r\n        </div>\r\n    </div>\r\n    \r\n    \r\n    \r\n    \r\n    <div class=\"copyright\">\r\n        <a title=\"官方网站\" href=\"http://www.tengfangyun.com\"></a>\r\n    </div>\r\n        <script>\r\n        var LINE = 803;\r\n\r\n        function $(selector, node){\r\n            var elements;\r\n\r\n            node = node || document;\r\n            if(document.querySelectorAll){\r\n                elements = node.querySelectorAll(selector);\r\n            } else {\r\n                switch(selector.substr(0, 1)){\r\n                    case '#':\r\n                        elements = [node.getElementById(selector.substr(1))];\r\n                        break;\r\n                    case '.':\r\n                        if(document.getElementsByClassName){\r\n                            elements = node.getElementsByClassName(selector.substr(1));\r\n                        } else {\r\n                            elements = get_elements_by_class(selector.substr(1), node);\r\n                        }\r\n                        break;\r\n                    default:\r\n                        elements = node.getElementsByTagName();\r\n                }\r\n            }\r\n            return elements;\r\n\r\n            function get_elements_by_class(search_class, node, tag) {\r\n                var elements = [], eles,\r\n                    pattern  = new RegExp('(^|\\\\s)' + search_class + '(\\\\s|$)');\r\n\r\n                node = node || document;\r\n                tag  = tag  || '*';\r\n\r\n                eles = node.getElementsByTagName(tag);\r\n                for(var i = 0; i < eles.length; i++) {\r\n                    if(pattern.test(eles[i].className)) {\r\n                        elements.push(eles[i])\r\n                    }\r\n                }\r\n\r\n                return elements;\r\n            }\r\n        }\r\n\r\n        $.getScript = function(src, func){\r\n            var script = document.createElement('script');\r\n\r\n            script.async  = 'async';\r\n            script.src    = src;\r\n            script.onload = func || function(){};\r\n\r\n            $('head')[0].appendChild(script);\r\n        }\r\n\r\n        ;(function(){\r\n            var files = $('.toggle');\r\n            var ol    = $('ol', $('.prettyprint')[0]);\r\n            var li    = $('li', ol[0]);\r\n\r\n            // 短路径和长路径变换\r\n            for(var i = 0; i < files.length; i++){\r\n                files[i].ondblclick = function(){\r\n                    var title = this.title;\r\n\r\n                    this.title = this.innerHTML;\r\n                    this.innerHTML = title;\r\n                }\r\n            }\r\n\r\n            // 设置出错行\r\n            var err_line = $('.line-' + LINE, ol[0])[0];\r\n            err_line.className = err_line.className + ' line-error';\r\n\r\n            $.getScript('//cdn.bootcss.com/prettify/r298/prettify.min.js', function(){\r\n                prettyPrint();\r\n\r\n                // 解决Firefox浏览器一个很诡异的问题\r\n                // 当代码高亮后，ol的行号莫名其妙的错位\r\n                // 但是只要刷新li里面的html重新渲染就没有问题了\r\n                if(window.navigator.userAgent.indexOf('Firefox') >= 0){\r\n                    ol[0].innerHTML = ol[0].innerHTML;\r\n                }\r\n            });\r\n\r\n        })();\r\n    </script>\r\n    </body>\r\n</html>\r\n", "httpCode": 500}
2025-07-29 11:23:24,185 - INFO - 推送链接https://www.tengfun.com/houseResource/saveHouseByLm,推送房源信息{"id": 9311, "trade_status": 9, "trade_type": 1, "total_floor": 6, "floor": 3, "loudong": "2号楼", "danyuan": "3单元", "mianji": 110, "fanghao": "503", "shi": 3, "ting": 2, "wei": 2, "rent_price": 0, "danjia": 20272, "is_top": 0, "sale_price": 2230000, "zhuangxiu": "毛坯", "chaoxiang": "南北", "agent_id": 5, "vr_url": "", "ctime": "2025-06-13 15:53:50", "utime": "2025-06-13 15:53:50", "label": "", "house_info": {"house_code": "", "house_structure": "", "building_type": "", "building_structure": "", "heating_type": "", "ownership": "", "property_certificate_period": "", "property_rights": "", "mortgage": "有抵押", "mortgage_price": 0, "elevator": "有电梯", "built_year": "", "elevator_desc": "1梯1户", "first_upload_at": "2025-06-13", "last_trade_at": "", "check_in_time": "", "check_in": "随时入住", "term": "", "see_time": "", "electricity": "民电", "water": "民水", "gas": "有", "term_type": "", "facilities": "", "parking": "有车位"}, "house_certificate": 0, "house_certificate_image": [], "house_follow_info": [], "community_name": "幸福西北区", "lat": 35.087132, "lng": 117.153954, "pic": [], "area": "滕州市", "region": "龙泉", "has_key": 1, "has_entrust": 0, "videos": [], "vrs": [], "tel": "13791416566"}
2025-07-29 11:23:25,089 - INFO - 推送房源信息返回结果{"content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>500</title>\r\n    <meta name=\"robots\" content=\"noindex,nofollow\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\r\n    <style>\r\n        /* Base */\r\n        body {\r\n            color: #333;\r\n            font: 14px Verdana, \"Helvetica Neue\", helvetica, Arial, 'Microsoft YaHei', sans-serif;\r\n            margin: 0;\r\n            padding: 0 20px 20px;\r\n            word-break: break-word;\r\n        }\r\n        h1{\r\n            margin: 10px 0 0;\r\n            font-size: 28px;\r\n            font-weight: 500;\r\n            line-height: 32px;\r\n        }\r\n        h2{\r\n            color: #4288ce;\r\n            font-weight: 400;\r\n            padding: 6px 0;\r\n            margin: 6px 0 0;\r\n            font-size: 18px;\r\n            border-bottom: 1px solid #eee;\r\n        }\r\n        h3.subheading {\r\n            color: #4288ce;\r\n            margin: 6px 0 0;\r\n            font-weight: 400;\r\n        }\r\n        h3{\r\n            margin: 12px;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n        }\r\n        abbr{\r\n            cursor: help;\r\n            text-decoration: underline;\r\n            text-decoration-style: dotted;\r\n        }\r\n        a{\r\n            color: #868686;\r\n            cursor: pointer;\r\n        }\r\n        a:hover{\r\n            text-decoration: underline;\r\n        }\r\n        .line-error{\r\n            background: #f8cbcb;\r\n        }\r\n\r\n        .echo table {\r\n            width: 100%;\r\n        }\r\n\r\n        .echo pre {\r\n            padding: 16px;\r\n            overflow: auto;\r\n            font-size: 85%;\r\n            line-height: 1.45;\r\n            background-color: #f7f7f7;\r\n            border: 0;\r\n            border-radius: 3px;\r\n            font-family: Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\r\n        }\r\n\r\n        .echo pre > pre {\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        /* Layout */\r\n        .col-md-3 {\r\n            width: 25%;\r\n        }\r\n        .col-md-9 {\r\n            width: 75%;\r\n        }\r\n        [class^=\"col-md-\"] {\r\n            float: left;\r\n        }\r\n        .clearfix {\r\n            clear:both;\r\n        }\r\n        @media only screen\r\n        and (min-device-width : 375px)\r\n        and (max-device-width : 667px) {\r\n            .col-md-3,\r\n            .col-md-9 {\r\n                width: 100%;\r\n            }\r\n        }\r\n        /* Exception Info */\r\n        .bg_exception{\r\n            background:url(\"../../static/skin/tfy/img/500bg.png\") no-repeat;\r\n            background-size:100% 100%;\r\n            text-align: center;\r\n            padding-top:55px;\r\n            height:500px;\r\n        }\r\n        \r\n        .bg_exception .look{\r\n          margin-top:75px;\r\n          color:#fb5050;\r\n        }\r\n        .bg_exception .look a {\r\n            color:#fb5050;\r\n            display:inline-block;\r\n            margin-right: 6px;\r\n            font-size: 14px;\r\n            text-decoration-line: none;\r\n         }\r\n        .bg_exception .look a:hover{\r\n            font-size: 15px;\r\n            font-weight:700;\r\n         }\r\n        .bg_exception .look span{\r\n            font-size: 14px;\r\n            margin-right: 5px;           \r\n        }\r\n        .bg_exception>div{\r\n            border-top:0px;\r\n        }\r\n\r\n        .exception {\r\n            margin-top: 20px;\r\n        }\r\n        .exception .message{\r\n            padding: 12px;\r\n            border: 1px solid #ddd;\r\n            border-bottom: 0 none;\r\n            line-height: 18px;\r\n            font-size:16px;\r\n            border-top-left-radius: 4px;\r\n            border-top-right-radius: 4px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n\r\n        .exception .code{\r\n            float: left;\r\n            text-align: center;\r\n            color: #fff;\r\n            margin-right: 12px;\r\n            padding: 16px;\r\n            border-radius: 4px;\r\n            background: #999;\r\n        }\r\n        .exception .source-code{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n\r\n            background: #f9f9f9;\r\n            overflow-x: auto;\r\n\r\n        }\r\n        .exception .source-code pre{\r\n            margin: 0;\r\n        }\r\n        .exception .source-code pre ol{\r\n            margin: 0;\r\n            color: #4288ce;\r\n            display: inline-block;\r\n            min-width: 100%;\r\n            box-sizing: border-box;\r\n            font-size:14px;\r\n            font-family: \"Century Gothic\",Consolas,\"Liberation Mono\",Courier,Verdana;\r\n            padding-left: 40px;\r\n        }\r\n        .exception .source-code pre li{\r\n            border-left: 1px solid #ddd;\r\n            height: 18px;\r\n            line-height: 18px;\r\n        }\r\n        .exception .source-code pre code{\r\n            color: #333;\r\n            height: 100%;\r\n            display: inline-block;\r\n            border-left: 1px solid #fff;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n            border-top: 0 none;\r\n            line-height: 16px;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace ol{\r\n            margin: 12px;\r\n        }\r\n        .exception .trace ol li{\r\n            padding: 2px 4px;\r\n        }\r\n        .exception div:last-child{\r\n            border-bottom-left-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        /* Exception Variables */\r\n        .exception-var table{\r\n            width: 100%;\r\n            margin: 12px 0;\r\n            box-sizing: border-box;\r\n            table-layout:fixed;\r\n            word-wrap:break-word;\r\n        }\r\n        .exception-var table caption{\r\n            text-align: left;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            padding: 6px 0;\r\n        }\r\n        .exception-var table caption small{\r\n            font-weight: 300;\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n            color: #ccc;\r\n        }\r\n        .exception-var table tbody{\r\n            font-size: 13px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,\"微软雅黑\";\r\n        }\r\n        .exception-var table td{\r\n            padding: 0 6px;\r\n            vertical-align: top;\r\n            word-break: break-all;\r\n        }\r\n        .exception-var table td:first-child{\r\n            width: 28%;\r\n            font-weight: bold;\r\n            white-space: nowrap;\r\n        }\r\n        .exception-var table td pre{\r\n            margin: 0;\r\n        }\r\n\r\n        /* Copyright Info */\r\n        .copyright{\r\n            margin-top: 24px;\r\n            padding: 12px 0;\r\n            border-top: 1px solid #eee;\r\n        }\r\n\r\n        /* SPAN elements with the classes below are added by prettyprint. */\r\n        pre.prettyprint .pln { color: #000 }  /* plain text */\r\n        pre.prettyprint .str { color: #080 }  /* string content */\r\n        pre.prettyprint .kwd { color: #008 }  /* a keyword */\r\n        pre.prettyprint .com { color: #800 }  /* a comment */\r\n        pre.prettyprint .typ { color: #606 }  /* a type name */\r\n        pre.prettyprint .lit { color: #066 }  /* a literal value */\r\n        /* punctuation, lisp open bracket, lisp close bracket */\r\n        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }\r\n        pre.prettyprint .tag { color: #008 }  /* a markup tag name */\r\n        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */\r\n        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */\r\n        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */\r\n        pre.prettyprint .fun { color: red }  /* a function name */\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"echo\">\r\n            </div>\r\n        <div class=\"exception bg_exception\">\r\n\r\n            <div class=\"info\"><h1>您的访问出错了啦。。</h1></div>\r\n        <div>\r\n            <p class=\"look\">\r\n                                <span>您可以查看更多:</span>\r\n                                <a href=\"/info/list-1.html\">住宅出售</a>\r\n                                <a href=\"/shangye/\">商业办公</a>\r\n                                <a href=\"/shangpu/\">商铺</a>\r\n                                <a href=\"/info/list-2.html\">住宅出租</a>\r\n                                <a href=\"/shangpu/list-2.html\">商铺出租</a>\r\n                                <a href=\"/xiezilou/list-2.html\">写字楼</a>\r\n                            </p>\r\n        </div>\r\n\r\n    </div>\r\n    \r\n    \r\n    \r\n    <div class=\"copyright\">\r\n        <a title=\"官方网站\" href=\"http://www.tengfangyun.com\"></a>\r\n    </div>\r\n    </body>\r\n</html>\r\n", "httpCode": 500}
2025-07-29 11:26:47,796 - INFO - 推送链接https://www.tengfun.com/houseResource/saveHouseByLm,推送房源信息{"id": 9311, "trade_status": 9, "trade_type": 1, "total_floor": 6, "floor": 3, "loudong": "2号楼", "danyuan": "3单元", "mianji": 110, "fanghao": "503", "shi": 3, "ting": 2, "wei": 2, "rent_price": 0, "danjia": 20272, "is_top": 0, "sale_price": 2230000, "zhuangxiu": "毛坯", "chaoxiang": "南北", "agent_id": 5, "vr_url": "", "ctime": "2025-06-13 15:53:50", "utime": "2025-06-13 15:53:50", "label": "", "house_info": {"house_code": "", "house_structure": "", "building_type": "", "building_structure": "", "heating_type": "", "ownership": "", "property_certificate_period": "", "property_rights": "", "mortgage": "有抵押", "mortgage_price": 0, "elevator": "有电梯", "built_year": "", "elevator_desc": "1梯1户", "first_upload_at": "2025-06-13", "last_trade_at": "", "check_in_time": "", "check_in": "随时入住", "term": "", "see_time": "", "electricity": "民电", "water": "民水", "gas": "有", "term_type": "", "facilities": "", "parking": "有车位"}, "house_certificate": 0, "house_certificate_image": [], "house_follow_info": [], "community_name": "幸福西北区", "lat": 35.087132, "lng": 117.153954, "pic": [], "area": "滕州市", "region": "龙泉", "has_key": 1, "has_entrust": 0, "videos": [], "vrs": [], "tel": "13791416566"}
2025-07-29 11:26:48,262 - INFO - 推送房源信息返回结果{"content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>500</title>\r\n    <meta name=\"robots\" content=\"noindex,nofollow\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\r\n    <style>\r\n        /* Base */\r\n        body {\r\n            color: #333;\r\n            font: 14px Verdana, \"Helvetica Neue\", helvetica, Arial, 'Microsoft YaHei', sans-serif;\r\n            margin: 0;\r\n            padding: 0 20px 20px;\r\n            word-break: break-word;\r\n        }\r\n        h1{\r\n            margin: 10px 0 0;\r\n            font-size: 28px;\r\n            font-weight: 500;\r\n            line-height: 32px;\r\n        }\r\n        h2{\r\n            color: #4288ce;\r\n            font-weight: 400;\r\n            padding: 6px 0;\r\n            margin: 6px 0 0;\r\n            font-size: 18px;\r\n            border-bottom: 1px solid #eee;\r\n        }\r\n        h3.subheading {\r\n            color: #4288ce;\r\n            margin: 6px 0 0;\r\n            font-weight: 400;\r\n        }\r\n        h3{\r\n            margin: 12px;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n        }\r\n        abbr{\r\n            cursor: help;\r\n            text-decoration: underline;\r\n            text-decoration-style: dotted;\r\n        }\r\n        a{\r\n            color: #868686;\r\n            cursor: pointer;\r\n        }\r\n        a:hover{\r\n            text-decoration: underline;\r\n        }\r\n        .line-error{\r\n            background: #f8cbcb;\r\n        }\r\n\r\n        .echo table {\r\n            width: 100%;\r\n        }\r\n\r\n        .echo pre {\r\n            padding: 16px;\r\n            overflow: auto;\r\n            font-size: 85%;\r\n            line-height: 1.45;\r\n            background-color: #f7f7f7;\r\n            border: 0;\r\n            border-radius: 3px;\r\n            font-family: Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\r\n        }\r\n\r\n        .echo pre > pre {\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        /* Layout */\r\n        .col-md-3 {\r\n            width: 25%;\r\n        }\r\n        .col-md-9 {\r\n            width: 75%;\r\n        }\r\n        [class^=\"col-md-\"] {\r\n            float: left;\r\n        }\r\n        .clearfix {\r\n            clear:both;\r\n        }\r\n        @media only screen\r\n        and (min-device-width : 375px)\r\n        and (max-device-width : 667px) {\r\n            .col-md-3,\r\n            .col-md-9 {\r\n                width: 100%;\r\n            }\r\n        }\r\n        /* Exception Info */\r\n        .bg_exception{\r\n            background:url(\"../../static/skin/tfy/img/500bg.png\") no-repeat;\r\n            background-size:100% 100%;\r\n            text-align: center;\r\n            padding-top:55px;\r\n            height:500px;\r\n        }\r\n        \r\n        .bg_exception .look{\r\n          margin-top:75px;\r\n          color:#fb5050;\r\n        }\r\n        .bg_exception .look a {\r\n            color:#fb5050;\r\n            display:inline-block;\r\n            margin-right: 6px;\r\n            font-size: 14px;\r\n            text-decoration-line: none;\r\n         }\r\n        .bg_exception .look a:hover{\r\n            font-size: 15px;\r\n            font-weight:700;\r\n         }\r\n        .bg_exception .look span{\r\n            font-size: 14px;\r\n            margin-right: 5px;           \r\n        }\r\n        .bg_exception>div{\r\n            border-top:0px;\r\n        }\r\n\r\n        .exception {\r\n            margin-top: 20px;\r\n        }\r\n        .exception .message{\r\n            padding: 12px;\r\n            border: 1px solid #ddd;\r\n            border-bottom: 0 none;\r\n            line-height: 18px;\r\n            font-size:16px;\r\n            border-top-left-radius: 4px;\r\n            border-top-right-radius: 4px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n\r\n        .exception .code{\r\n            float: left;\r\n            text-align: center;\r\n            color: #fff;\r\n            margin-right: 12px;\r\n            padding: 16px;\r\n            border-radius: 4px;\r\n            background: #999;\r\n        }\r\n        .exception .source-code{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n\r\n            background: #f9f9f9;\r\n            overflow-x: auto;\r\n\r\n        }\r\n        .exception .source-code pre{\r\n            margin: 0;\r\n        }\r\n        .exception .source-code pre ol{\r\n            margin: 0;\r\n            color: #4288ce;\r\n            display: inline-block;\r\n            min-width: 100%;\r\n            box-sizing: border-box;\r\n            font-size:14px;\r\n            font-family: \"Century Gothic\",Consolas,\"Liberation Mono\",Courier,Verdana;\r\n            padding-left: 40px;\r\n        }\r\n        .exception .source-code pre li{\r\n            border-left: 1px solid #ddd;\r\n            height: 18px;\r\n            line-height: 18px;\r\n        }\r\n        .exception .source-code pre code{\r\n            color: #333;\r\n            height: 100%;\r\n            display: inline-block;\r\n            border-left: 1px solid #fff;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n            border-top: 0 none;\r\n            line-height: 16px;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace ol{\r\n            margin: 12px;\r\n        }\r\n        .exception .trace ol li{\r\n            padding: 2px 4px;\r\n        }\r\n        .exception div:last-child{\r\n            border-bottom-left-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        /* Exception Variables */\r\n        .exception-var table{\r\n            width: 100%;\r\n            margin: 12px 0;\r\n            box-sizing: border-box;\r\n            table-layout:fixed;\r\n            word-wrap:break-word;\r\n        }\r\n        .exception-var table caption{\r\n            text-align: left;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            padding: 6px 0;\r\n        }\r\n        .exception-var table caption small{\r\n            font-weight: 300;\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n            color: #ccc;\r\n        }\r\n        .exception-var table tbody{\r\n            font-size: 13px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,\"微软雅黑\";\r\n        }\r\n        .exception-var table td{\r\n            padding: 0 6px;\r\n            vertical-align: top;\r\n            word-break: break-all;\r\n        }\r\n        .exception-var table td:first-child{\r\n            width: 28%;\r\n            font-weight: bold;\r\n            white-space: nowrap;\r\n        }\r\n        .exception-var table td pre{\r\n            margin: 0;\r\n        }\r\n\r\n        /* Copyright Info */\r\n        .copyright{\r\n            margin-top: 24px;\r\n            padding: 12px 0;\r\n            border-top: 1px solid #eee;\r\n        }\r\n\r\n        /* SPAN elements with the classes below are added by prettyprint. */\r\n        pre.prettyprint .pln { color: #000 }  /* plain text */\r\n        pre.prettyprint .str { color: #080 }  /* string content */\r\n        pre.prettyprint .kwd { color: #008 }  /* a keyword */\r\n        pre.prettyprint .com { color: #800 }  /* a comment */\r\n        pre.prettyprint .typ { color: #606 }  /* a type name */\r\n        pre.prettyprint .lit { color: #066 }  /* a literal value */\r\n        /* punctuation, lisp open bracket, lisp close bracket */\r\n        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }\r\n        pre.prettyprint .tag { color: #008 }  /* a markup tag name */\r\n        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */\r\n        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */\r\n        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */\r\n        pre.prettyprint .fun { color: red }  /* a function name */\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"echo\">\r\n            </div>\r\n        <div class=\"exception bg_exception\">\r\n\r\n            <div class=\"info\"><h1>您的访问出错了啦。。</h1></div>\r\n        <div>\r\n            <p class=\"look\">\r\n                                <span>您可以查看更多:</span>\r\n                                <a href=\"/info/list-1.html\">住宅出售</a>\r\n                                <a href=\"/shangye/\">商业办公</a>\r\n                                <a href=\"/shangpu/\">商铺</a>\r\n                                <a href=\"/info/list-2.html\">住宅出租</a>\r\n                                <a href=\"/shangpu/list-2.html\">商铺出租</a>\r\n                                <a href=\"/xiezilou/list-2.html\">写字楼</a>\r\n                            </p>\r\n        </div>\r\n\r\n    </div>\r\n    \r\n    \r\n    \r\n    <div class=\"copyright\">\r\n        <a title=\"官方网站\" href=\"http://www.tengfangyun.com\"></a>\r\n    </div>\r\n    </body>\r\n</html>\r\n", "httpCode": 500}
2025-07-29 11:27:48,471 - INFO - 推送链接https://www.tengfun.com/houseResource/saveHouseByLm,推送房源信息{"id": 9311, "trade_status": 9, "trade_type": 1, "total_floor": 6, "floor": 3, "loudong": "2号楼", "danyuan": "3单元", "mianji": 110, "fanghao": "503", "shi": 3, "ting": 2, "wei": 2, "rent_price": 0, "danjia": 20272, "is_top": 0, "sale_price": 2230000, "zhuangxiu": "毛坯", "chaoxiang": "南北", "agent_id": 5, "vr_url": "", "ctime": "2025-06-13 15:53:50", "utime": "2025-06-13 15:53:50", "label": "", "house_info": {"house_code": "", "house_structure": "", "building_type": "", "building_structure": "", "heating_type": "", "ownership": "", "property_certificate_period": "", "property_rights": "", "mortgage": "有抵押", "mortgage_price": 0, "elevator": "有电梯", "built_year": "", "elevator_desc": "1梯1户", "first_upload_at": "2025-06-13", "last_trade_at": "", "check_in_time": "", "check_in": "随时入住", "term": "", "see_time": "", "electricity": "民电", "water": "民水", "gas": "有", "term_type": "", "facilities": "", "parking": "有车位"}, "house_certificate": 0, "house_certificate_image": [], "house_follow_info": [], "community_name": "幸福西北区", "lat": 35.087132, "lng": 117.153954, "pic": [], "area": "滕州市", "region": "龙泉", "has_key": 1, "has_entrust": 0, "videos": [], "vrs": [], "tel": "13791416566"}
2025-07-29 11:27:48,919 - INFO - 推送房源信息返回结果{"content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>500</title>\r\n    <meta name=\"robots\" content=\"noindex,nofollow\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\r\n    <style>\r\n        /* Base */\r\n        body {\r\n            color: #333;\r\n            font: 14px Verdana, \"Helvetica Neue\", helvetica, Arial, 'Microsoft YaHei', sans-serif;\r\n            margin: 0;\r\n            padding: 0 20px 20px;\r\n            word-break: break-word;\r\n        }\r\n        h1{\r\n            margin: 10px 0 0;\r\n            font-size: 28px;\r\n            font-weight: 500;\r\n            line-height: 32px;\r\n        }\r\n        h2{\r\n            color: #4288ce;\r\n            font-weight: 400;\r\n            padding: 6px 0;\r\n            margin: 6px 0 0;\r\n            font-size: 18px;\r\n            border-bottom: 1px solid #eee;\r\n        }\r\n        h3.subheading {\r\n            color: #4288ce;\r\n            margin: 6px 0 0;\r\n            font-weight: 400;\r\n        }\r\n        h3{\r\n            margin: 12px;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n        }\r\n        abbr{\r\n            cursor: help;\r\n            text-decoration: underline;\r\n            text-decoration-style: dotted;\r\n        }\r\n        a{\r\n            color: #868686;\r\n            cursor: pointer;\r\n        }\r\n        a:hover{\r\n            text-decoration: underline;\r\n        }\r\n        .line-error{\r\n            background: #f8cbcb;\r\n        }\r\n\r\n        .echo table {\r\n            width: 100%;\r\n        }\r\n\r\n        .echo pre {\r\n            padding: 16px;\r\n            overflow: auto;\r\n            font-size: 85%;\r\n            line-height: 1.45;\r\n            background-color: #f7f7f7;\r\n            border: 0;\r\n            border-radius: 3px;\r\n            font-family: Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\r\n        }\r\n\r\n        .echo pre > pre {\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        /* Layout */\r\n        .col-md-3 {\r\n            width: 25%;\r\n        }\r\n        .col-md-9 {\r\n            width: 75%;\r\n        }\r\n        [class^=\"col-md-\"] {\r\n            float: left;\r\n        }\r\n        .clearfix {\r\n            clear:both;\r\n        }\r\n        @media only screen\r\n        and (min-device-width : 375px)\r\n        and (max-device-width : 667px) {\r\n            .col-md-3,\r\n            .col-md-9 {\r\n                width: 100%;\r\n            }\r\n        }\r\n        /* Exception Info */\r\n        .bg_exception{\r\n            background:url(\"../../static/skin/tfy/img/500bg.png\") no-repeat;\r\n            background-size:100% 100%;\r\n            text-align: center;\r\n            padding-top:55px;\r\n            height:500px;\r\n        }\r\n        \r\n        .bg_exception .look{\r\n          margin-top:75px;\r\n          color:#fb5050;\r\n        }\r\n        .bg_exception .look a {\r\n            color:#fb5050;\r\n            display:inline-block;\r\n            margin-right: 6px;\r\n            font-size: 14px;\r\n            text-decoration-line: none;\r\n         }\r\n        .bg_exception .look a:hover{\r\n            font-size: 15px;\r\n            font-weight:700;\r\n         }\r\n        .bg_exception .look span{\r\n            font-size: 14px;\r\n            margin-right: 5px;           \r\n        }\r\n        .bg_exception>div{\r\n            border-top:0px;\r\n        }\r\n\r\n        .exception {\r\n            margin-top: 20px;\r\n        }\r\n        .exception .message{\r\n            padding: 12px;\r\n            border: 1px solid #ddd;\r\n            border-bottom: 0 none;\r\n            line-height: 18px;\r\n            font-size:16px;\r\n            border-top-left-radius: 4px;\r\n            border-top-right-radius: 4px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n\r\n        .exception .code{\r\n            float: left;\r\n            text-align: center;\r\n            color: #fff;\r\n            margin-right: 12px;\r\n            padding: 16px;\r\n            border-radius: 4px;\r\n            background: #999;\r\n        }\r\n        .exception .source-code{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n\r\n            background: #f9f9f9;\r\n            overflow-x: auto;\r\n\r\n        }\r\n        .exception .source-code pre{\r\n            margin: 0;\r\n        }\r\n        .exception .source-code pre ol{\r\n            margin: 0;\r\n            color: #4288ce;\r\n            display: inline-block;\r\n            min-width: 100%;\r\n            box-sizing: border-box;\r\n            font-size:14px;\r\n            font-family: \"Century Gothic\",Consolas,\"Liberation Mono\",Courier,Verdana;\r\n            padding-left: 40px;\r\n        }\r\n        .exception .source-code pre li{\r\n            border-left: 1px solid #ddd;\r\n            height: 18px;\r\n            line-height: 18px;\r\n        }\r\n        .exception .source-code pre code{\r\n            color: #333;\r\n            height: 100%;\r\n            display: inline-block;\r\n            border-left: 1px solid #fff;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n            border-top: 0 none;\r\n            line-height: 16px;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace ol{\r\n            margin: 12px;\r\n        }\r\n        .exception .trace ol li{\r\n            padding: 2px 4px;\r\n        }\r\n        .exception div:last-child{\r\n            border-bottom-left-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        /* Exception Variables */\r\n        .exception-var table{\r\n            width: 100%;\r\n            margin: 12px 0;\r\n            box-sizing: border-box;\r\n            table-layout:fixed;\r\n            word-wrap:break-word;\r\n        }\r\n        .exception-var table caption{\r\n            text-align: left;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            padding: 6px 0;\r\n        }\r\n        .exception-var table caption small{\r\n            font-weight: 300;\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n            color: #ccc;\r\n        }\r\n        .exception-var table tbody{\r\n            font-size: 13px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,\"微软雅黑\";\r\n        }\r\n        .exception-var table td{\r\n            padding: 0 6px;\r\n            vertical-align: top;\r\n            word-break: break-all;\r\n        }\r\n        .exception-var table td:first-child{\r\n            width: 28%;\r\n            font-weight: bold;\r\n            white-space: nowrap;\r\n        }\r\n        .exception-var table td pre{\r\n            margin: 0;\r\n        }\r\n\r\n        /* Copyright Info */\r\n        .copyright{\r\n            margin-top: 24px;\r\n            padding: 12px 0;\r\n            border-top: 1px solid #eee;\r\n        }\r\n\r\n        /* SPAN elements with the classes below are added by prettyprint. */\r\n        pre.prettyprint .pln { color: #000 }  /* plain text */\r\n        pre.prettyprint .str { color: #080 }  /* string content */\r\n        pre.prettyprint .kwd { color: #008 }  /* a keyword */\r\n        pre.prettyprint .com { color: #800 }  /* a comment */\r\n        pre.prettyprint .typ { color: #606 }  /* a type name */\r\n        pre.prettyprint .lit { color: #066 }  /* a literal value */\r\n        /* punctuation, lisp open bracket, lisp close bracket */\r\n        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }\r\n        pre.prettyprint .tag { color: #008 }  /* a markup tag name */\r\n        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */\r\n        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */\r\n        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */\r\n        pre.prettyprint .fun { color: red }  /* a function name */\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"echo\">\r\n            </div>\r\n        <div class=\"exception bg_exception\">\r\n\r\n            <div class=\"info\"><h1>您的访问出错了啦。。</h1></div>\r\n        <div>\r\n            <p class=\"look\">\r\n                                <span>您可以查看更多:</span>\r\n                                <a href=\"/info/list-1.html\">住宅出售</a>\r\n                                <a href=\"/shangye/\">商业办公</a>\r\n                                <a href=\"/shangpu/\">商铺</a>\r\n                                <a href=\"/info/list-2.html\">住宅出租</a>\r\n                                <a href=\"/shangpu/list-2.html\">商铺出租</a>\r\n                                <a href=\"/xiezilou/list-2.html\">写字楼</a>\r\n                            </p>\r\n        </div>\r\n\r\n    </div>\r\n    \r\n    \r\n    \r\n    <div class=\"copyright\">\r\n        <a title=\"官方网站\" href=\"http://www.tengfangyun.com\"></a>\r\n    </div>\r\n    </body>\r\n</html>\r\n", "httpCode": 500}
2025-07-29 11:31:19,731 - INFO - 推送链接https://www.tengfun.com/houseResource/saveHouseByLm,推送房源信息{"id": 9311, "trade_status": 9, "trade_type": 1, "total_floor": 6, "floor": 3, "loudong": "2号楼", "danyuan": "3单元", "mianji": 110, "fanghao": "503", "shi": 3, "ting": 2, "wei": 2, "rent_price": 0, "danjia": 20272, "is_top": 0, "sale_price": 2230000, "zhuangxiu": "毛坯", "chaoxiang": "南北", "agent_id": 5, "vr_url": "", "ctime": "2025-06-13 15:53:50", "utime": "2025-06-13 15:53:50", "label": "", "house_info": {"house_code": "", "house_structure": "", "building_type": "", "building_structure": "", "heating_type": "", "ownership": "", "property_certificate_period": "", "property_rights": "", "mortgage": "有抵押", "mortgage_price": 0, "elevator": "有电梯", "built_year": "", "elevator_desc": "1梯1户", "first_upload_at": "2025-06-13", "last_trade_at": "", "check_in_time": "", "check_in": "随时入住", "term": "", "see_time": "", "electricity": "民电", "water": "民水", "gas": "有", "term_type": "", "facilities": "", "parking": "有车位"}, "house_certificate": 0, "house_certificate_image": [], "house_follow_info": [], "community_name": "幸福西北区", "lat": 35.087132, "lng": 117.153954, "pic": [], "area": "滕州市", "region": "龙泉", "has_key": 1, "has_entrust": 0, "videos": [], "vrs": [], "tel": "13791416566"}
2025-07-29 11:31:20,190 - INFO - 推送房源信息返回结果{"content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>500</title>\r\n    <meta name=\"robots\" content=\"noindex,nofollow\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\r\n    <style>\r\n        /* Base */\r\n        body {\r\n            color: #333;\r\n            font: 14px Verdana, \"Helvetica Neue\", helvetica, Arial, 'Microsoft YaHei', sans-serif;\r\n            margin: 0;\r\n            padding: 0 20px 20px;\r\n            word-break: break-word;\r\n        }\r\n        h1{\r\n            margin: 10px 0 0;\r\n            font-size: 28px;\r\n            font-weight: 500;\r\n            line-height: 32px;\r\n        }\r\n        h2{\r\n            color: #4288ce;\r\n            font-weight: 400;\r\n            padding: 6px 0;\r\n            margin: 6px 0 0;\r\n            font-size: 18px;\r\n            border-bottom: 1px solid #eee;\r\n        }\r\n        h3.subheading {\r\n            color: #4288ce;\r\n            margin: 6px 0 0;\r\n            font-weight: 400;\r\n        }\r\n        h3{\r\n            margin: 12px;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n        }\r\n        abbr{\r\n            cursor: help;\r\n            text-decoration: underline;\r\n            text-decoration-style: dotted;\r\n        }\r\n        a{\r\n            color: #868686;\r\n            cursor: pointer;\r\n        }\r\n        a:hover{\r\n            text-decoration: underline;\r\n        }\r\n        .line-error{\r\n            background: #f8cbcb;\r\n        }\r\n\r\n        .echo table {\r\n            width: 100%;\r\n        }\r\n\r\n        .echo pre {\r\n            padding: 16px;\r\n            overflow: auto;\r\n            font-size: 85%;\r\n            line-height: 1.45;\r\n            background-color: #f7f7f7;\r\n            border: 0;\r\n            border-radius: 3px;\r\n            font-family: Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\r\n        }\r\n\r\n        .echo pre > pre {\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        /* Layout */\r\n        .col-md-3 {\r\n            width: 25%;\r\n        }\r\n        .col-md-9 {\r\n            width: 75%;\r\n        }\r\n        [class^=\"col-md-\"] {\r\n            float: left;\r\n        }\r\n        .clearfix {\r\n            clear:both;\r\n        }\r\n        @media only screen\r\n        and (min-device-width : 375px)\r\n        and (max-device-width : 667px) {\r\n            .col-md-3,\r\n            .col-md-9 {\r\n                width: 100%;\r\n            }\r\n        }\r\n        /* Exception Info */\r\n        .bg_exception{\r\n            background:url(\"../../static/skin/tfy/img/500bg.png\") no-repeat;\r\n            background-size:100% 100%;\r\n            text-align: center;\r\n            padding-top:55px;\r\n            height:500px;\r\n        }\r\n        \r\n        .bg_exception .look{\r\n          margin-top:75px;\r\n          color:#fb5050;\r\n        }\r\n        .bg_exception .look a {\r\n            color:#fb5050;\r\n            display:inline-block;\r\n            margin-right: 6px;\r\n            font-size: 14px;\r\n            text-decoration-line: none;\r\n         }\r\n        .bg_exception .look a:hover{\r\n            font-size: 15px;\r\n            font-weight:700;\r\n         }\r\n        .bg_exception .look span{\r\n            font-size: 14px;\r\n            margin-right: 5px;           \r\n        }\r\n        .bg_exception>div{\r\n            border-top:0px;\r\n        }\r\n\r\n        .exception {\r\n            margin-top: 20px;\r\n        }\r\n        .exception .message{\r\n            padding: 12px;\r\n            border: 1px solid #ddd;\r\n            border-bottom: 0 none;\r\n            line-height: 18px;\r\n            font-size:16px;\r\n            border-top-left-radius: 4px;\r\n            border-top-right-radius: 4px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n\r\n        .exception .code{\r\n            float: left;\r\n            text-align: center;\r\n            color: #fff;\r\n            margin-right: 12px;\r\n            padding: 16px;\r\n            border-radius: 4px;\r\n            background: #999;\r\n        }\r\n        .exception .source-code{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n\r\n            background: #f9f9f9;\r\n            overflow-x: auto;\r\n\r\n        }\r\n        .exception .source-code pre{\r\n            margin: 0;\r\n        }\r\n        .exception .source-code pre ol{\r\n            margin: 0;\r\n            color: #4288ce;\r\n            display: inline-block;\r\n            min-width: 100%;\r\n            box-sizing: border-box;\r\n            font-size:14px;\r\n            font-family: \"Century Gothic\",Consolas,\"Liberation Mono\",Courier,Verdana;\r\n            padding-left: 40px;\r\n        }\r\n        .exception .source-code pre li{\r\n            border-left: 1px solid #ddd;\r\n            height: 18px;\r\n            line-height: 18px;\r\n        }\r\n        .exception .source-code pre code{\r\n            color: #333;\r\n            height: 100%;\r\n            display: inline-block;\r\n            border-left: 1px solid #fff;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n            border-top: 0 none;\r\n            line-height: 16px;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace ol{\r\n            margin: 12px;\r\n        }\r\n        .exception .trace ol li{\r\n            padding: 2px 4px;\r\n        }\r\n        .exception div:last-child{\r\n            border-bottom-left-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        /* Exception Variables */\r\n        .exception-var table{\r\n            width: 100%;\r\n            margin: 12px 0;\r\n            box-sizing: border-box;\r\n            table-layout:fixed;\r\n            word-wrap:break-word;\r\n        }\r\n        .exception-var table caption{\r\n            text-align: left;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            padding: 6px 0;\r\n        }\r\n        .exception-var table caption small{\r\n            font-weight: 300;\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n            color: #ccc;\r\n        }\r\n        .exception-var table tbody{\r\n            font-size: 13px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,\"微软雅黑\";\r\n        }\r\n        .exception-var table td{\r\n            padding: 0 6px;\r\n            vertical-align: top;\r\n            word-break: break-all;\r\n        }\r\n        .exception-var table td:first-child{\r\n            width: 28%;\r\n            font-weight: bold;\r\n            white-space: nowrap;\r\n        }\r\n        .exception-var table td pre{\r\n            margin: 0;\r\n        }\r\n\r\n        /* Copyright Info */\r\n        .copyright{\r\n            margin-top: 24px;\r\n            padding: 12px 0;\r\n            border-top: 1px solid #eee;\r\n        }\r\n\r\n        /* SPAN elements with the classes below are added by prettyprint. */\r\n        pre.prettyprint .pln { color: #000 }  /* plain text */\r\n        pre.prettyprint .str { color: #080 }  /* string content */\r\n        pre.prettyprint .kwd { color: #008 }  /* a keyword */\r\n        pre.prettyprint .com { color: #800 }  /* a comment */\r\n        pre.prettyprint .typ { color: #606 }  /* a type name */\r\n        pre.prettyprint .lit { color: #066 }  /* a literal value */\r\n        /* punctuation, lisp open bracket, lisp close bracket */\r\n        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }\r\n        pre.prettyprint .tag { color: #008 }  /* a markup tag name */\r\n        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */\r\n        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */\r\n        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */\r\n        pre.prettyprint .fun { color: red }  /* a function name */\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"echo\">\r\n            </div>\r\n        <div class=\"exception bg_exception\">\r\n\r\n            <div class=\"info\"><h1>您的访问出错了啦。。</h1></div>\r\n        <div>\r\n            <p class=\"look\">\r\n                                <span>您可以查看更多:</span>\r\n                                <a href=\"/info/list-1.html\">住宅出售</a>\r\n                                <a href=\"/shangye/\">商业办公</a>\r\n                                <a href=\"/shangpu/\">商铺</a>\r\n                                <a href=\"/info/list-2.html\">住宅出租</a>\r\n                                <a href=\"/shangpu/list-2.html\">商铺出租</a>\r\n                                <a href=\"/xiezilou/list-2.html\">写字楼</a>\r\n                            </p>\r\n        </div>\r\n\r\n    </div>\r\n    \r\n    \r\n    \r\n    <div class=\"copyright\">\r\n        <a title=\"官方网站\" href=\"http://www.tengfangyun.com\"></a>\r\n    </div>\r\n    </body>\r\n</html>\r\n", "httpCode": 500}
2025-07-29 11:41:19,877 - INFO - 推送链接https://www.tengfun.com/houseResource/saveHouseByLm,推送房源信息{"id": 9311, "trade_status": 9, "trade_type": 1, "total_floor": 6, "floor": 3, "loudong": "2号楼", "danyuan": "3单元", "mianji": 110, "fanghao": "503", "shi": 3, "ting": 2, "wei": 2, "rent_price": 0, "danjia": 20272, "is_top": 0, "sale_price": 2230000, "zhuangxiu": "毛坯", "chaoxiang": "南北", "agent_id": 5, "vr_url": "", "ctime": "2025-06-13 15:53:50", "utime": "2025-06-13 15:53:50", "label": "", "house_info": {"house_code": "", "house_structure": "", "building_type": "", "building_structure": "", "heating_type": "", "ownership": "", "property_certificate_period": "", "property_rights": "", "mortgage": "有抵押", "mortgage_price": 0, "elevator": "有电梯", "built_year": "", "elevator_desc": "1梯1户", "first_upload_at": "2025-06-13", "last_trade_at": "", "check_in_time": "", "check_in": "随时入住", "term": "", "see_time": "", "electricity": "民电", "water": "民水", "gas": "有", "term_type": "", "facilities": "", "parking": "有车位"}, "house_certificate": 0, "house_certificate_image": [], "house_follow_info": [], "community_name": "幸福西北区", "lat": 35.087132, "lng": 117.153954, "pic": [], "area": "滕州市", "region": "龙泉", "has_key": 1, "has_entrust": 0, "videos": [], "vrs": [], "tel": "13791416566"}
2025-07-29 11:41:20,381 - INFO - 推送房源信息返回结果{"content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>500</title>\r\n    <meta name=\"robots\" content=\"noindex,nofollow\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\r\n    <style>\r\n        /* Base */\r\n        body {\r\n            color: #333;\r\n            font: 14px Verdana, \"Helvetica Neue\", helvetica, Arial, 'Microsoft YaHei', sans-serif;\r\n            margin: 0;\r\n            padding: 0 20px 20px;\r\n            word-break: break-word;\r\n        }\r\n        h1{\r\n            margin: 10px 0 0;\r\n            font-size: 28px;\r\n            font-weight: 500;\r\n            line-height: 32px;\r\n        }\r\n        h2{\r\n            color: #4288ce;\r\n            font-weight: 400;\r\n            padding: 6px 0;\r\n            margin: 6px 0 0;\r\n            font-size: 18px;\r\n            border-bottom: 1px solid #eee;\r\n        }\r\n        h3.subheading {\r\n            color: #4288ce;\r\n            margin: 6px 0 0;\r\n            font-weight: 400;\r\n        }\r\n        h3{\r\n            margin: 12px;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n        }\r\n        abbr{\r\n            cursor: help;\r\n            text-decoration: underline;\r\n            text-decoration-style: dotted;\r\n        }\r\n        a{\r\n            color: #868686;\r\n            cursor: pointer;\r\n        }\r\n        a:hover{\r\n            text-decoration: underline;\r\n        }\r\n        .line-error{\r\n            background: #f8cbcb;\r\n        }\r\n\r\n        .echo table {\r\n            width: 100%;\r\n        }\r\n\r\n        .echo pre {\r\n            padding: 16px;\r\n            overflow: auto;\r\n            font-size: 85%;\r\n            line-height: 1.45;\r\n            background-color: #f7f7f7;\r\n            border: 0;\r\n            border-radius: 3px;\r\n            font-family: Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\r\n        }\r\n\r\n        .echo pre > pre {\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        /* Layout */\r\n        .col-md-3 {\r\n            width: 25%;\r\n        }\r\n        .col-md-9 {\r\n            width: 75%;\r\n        }\r\n        [class^=\"col-md-\"] {\r\n            float: left;\r\n        }\r\n        .clearfix {\r\n            clear:both;\r\n        }\r\n        @media only screen\r\n        and (min-device-width : 375px)\r\n        and (max-device-width : 667px) {\r\n            .col-md-3,\r\n            .col-md-9 {\r\n                width: 100%;\r\n            }\r\n        }\r\n        /* Exception Info */\r\n        .bg_exception{\r\n            background:url(\"../../static/skin/tfy/img/500bg.png\") no-repeat;\r\n            background-size:100% 100%;\r\n            text-align: center;\r\n            padding-top:55px;\r\n            height:500px;\r\n        }\r\n        \r\n        .bg_exception .look{\r\n          margin-top:75px;\r\n          color:#fb5050;\r\n        }\r\n        .bg_exception .look a {\r\n            color:#fb5050;\r\n            display:inline-block;\r\n            margin-right: 6px;\r\n            font-size: 14px;\r\n            text-decoration-line: none;\r\n         }\r\n        .bg_exception .look a:hover{\r\n            font-size: 15px;\r\n            font-weight:700;\r\n         }\r\n        .bg_exception .look span{\r\n            font-size: 14px;\r\n            margin-right: 5px;           \r\n        }\r\n        .bg_exception>div{\r\n            border-top:0px;\r\n        }\r\n\r\n        .exception {\r\n            margin-top: 20px;\r\n        }\r\n        .exception .message{\r\n            padding: 12px;\r\n            border: 1px solid #ddd;\r\n            border-bottom: 0 none;\r\n            line-height: 18px;\r\n            font-size:16px;\r\n            border-top-left-radius: 4px;\r\n            border-top-right-radius: 4px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n\r\n        .exception .code{\r\n            float: left;\r\n            text-align: center;\r\n            color: #fff;\r\n            margin-right: 12px;\r\n            padding: 16px;\r\n            border-radius: 4px;\r\n            background: #999;\r\n        }\r\n        .exception .source-code{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n\r\n            background: #f9f9f9;\r\n            overflow-x: auto;\r\n\r\n        }\r\n        .exception .source-code pre{\r\n            margin: 0;\r\n        }\r\n        .exception .source-code pre ol{\r\n            margin: 0;\r\n            color: #4288ce;\r\n            display: inline-block;\r\n            min-width: 100%;\r\n            box-sizing: border-box;\r\n            font-size:14px;\r\n            font-family: \"Century Gothic\",Consolas,\"Liberation Mono\",Courier,Verdana;\r\n            padding-left: 40px;\r\n        }\r\n        .exception .source-code pre li{\r\n            border-left: 1px solid #ddd;\r\n            height: 18px;\r\n            line-height: 18px;\r\n        }\r\n        .exception .source-code pre code{\r\n            color: #333;\r\n            height: 100%;\r\n            display: inline-block;\r\n            border-left: 1px solid #fff;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n            border-top: 0 none;\r\n            line-height: 16px;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace ol{\r\n            margin: 12px;\r\n        }\r\n        .exception .trace ol li{\r\n            padding: 2px 4px;\r\n        }\r\n        .exception div:last-child{\r\n            border-bottom-left-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        /* Exception Variables */\r\n        .exception-var table{\r\n            width: 100%;\r\n            margin: 12px 0;\r\n            box-sizing: border-box;\r\n            table-layout:fixed;\r\n            word-wrap:break-word;\r\n        }\r\n        .exception-var table caption{\r\n            text-align: left;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            padding: 6px 0;\r\n        }\r\n        .exception-var table caption small{\r\n            font-weight: 300;\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n            color: #ccc;\r\n        }\r\n        .exception-var table tbody{\r\n            font-size: 13px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,\"微软雅黑\";\r\n        }\r\n        .exception-var table td{\r\n            padding: 0 6px;\r\n            vertical-align: top;\r\n            word-break: break-all;\r\n        }\r\n        .exception-var table td:first-child{\r\n            width: 28%;\r\n            font-weight: bold;\r\n            white-space: nowrap;\r\n        }\r\n        .exception-var table td pre{\r\n            margin: 0;\r\n        }\r\n\r\n        /* Copyright Info */\r\n        .copyright{\r\n            margin-top: 24px;\r\n            padding: 12px 0;\r\n            border-top: 1px solid #eee;\r\n        }\r\n\r\n        /* SPAN elements with the classes below are added by prettyprint. */\r\n        pre.prettyprint .pln { color: #000 }  /* plain text */\r\n        pre.prettyprint .str { color: #080 }  /* string content */\r\n        pre.prettyprint .kwd { color: #008 }  /* a keyword */\r\n        pre.prettyprint .com { color: #800 }  /* a comment */\r\n        pre.prettyprint .typ { color: #606 }  /* a type name */\r\n        pre.prettyprint .lit { color: #066 }  /* a literal value */\r\n        /* punctuation, lisp open bracket, lisp close bracket */\r\n        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }\r\n        pre.prettyprint .tag { color: #008 }  /* a markup tag name */\r\n        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */\r\n        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */\r\n        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */\r\n        pre.prettyprint .fun { color: red }  /* a function name */\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"echo\">\r\n            </div>\r\n        <div class=\"exception bg_exception\">\r\n\r\n            <div class=\"info\"><h1>您的访问出错了啦。。</h1></div>\r\n        <div>\r\n            <p class=\"look\">\r\n                                <span>您可以查看更多:</span>\r\n                                <a href=\"/info/list-1.html\">住宅出售</a>\r\n                                <a href=\"/shangye/\">商业办公</a>\r\n                                <a href=\"/shangpu/\">商铺</a>\r\n                                <a href=\"/info/list-2.html\">住宅出租</a>\r\n                                <a href=\"/shangpu/list-2.html\">商铺出租</a>\r\n                                <a href=\"/xiezilou/list-2.html\">写字楼</a>\r\n                            </p>\r\n        </div>\r\n\r\n    </div>\r\n    \r\n    \r\n    \r\n    <div class=\"copyright\">\r\n        <a title=\"官方网站\" href=\"http://www.tengfangyun.com\"></a>\r\n    </div>\r\n    </body>\r\n</html>\r\n", "httpCode": 500}
2025-07-29 11:43:57,686 - INFO - 推送链接https://www.tengfun.com/houseResource/saveHouseByLm,推送房源信息{"id": 9311, "trade_status": 9, "trade_type": 1, "total_floor": 6, "floor": 3, "loudong": "2号楼", "danyuan": "3单元", "mianji": 110, "fanghao": "503", "shi": 3, "ting": 2, "wei": 2, "rent_price": 0, "danjia": 20272, "is_top": 0, "sale_price": 2230000, "zhuangxiu": "毛坯", "chaoxiang": "南北", "agent_id": 5, "vr_url": "", "ctime": "2025-06-13 15:53:50", "utime": "2025-06-13 15:53:50", "label": "", "house_info": {"house_code": "", "house_structure": "", "building_type": "", "building_structure": "", "heating_type": "", "ownership": "", "property_certificate_period": "", "property_rights": "", "mortgage": "有抵押", "mortgage_price": 0, "elevator": "有电梯", "built_year": "", "elevator_desc": "1梯1户", "first_upload_at": "2025-06-13", "last_trade_at": "", "check_in_time": "", "check_in": "随时入住", "term": "", "see_time": "", "electricity": "民电", "water": "民水", "gas": "有", "term_type": "", "facilities": "", "parking": "有车位"}, "house_certificate": 0, "house_certificate_image": [], "house_follow_info": [], "community_name": "幸福西北区", "lat": 35.087132, "lng": 117.153954, "pic": [], "area": "滕州市", "region": "龙泉", "has_key": 1, "has_entrust": 0, "videos": [], "vrs": [], "tel": "13791416566"}
2025-07-29 11:43:58,393 - INFO - 推送房源信息返回结果{"content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>500</title>\r\n    <meta name=\"robots\" content=\"noindex,nofollow\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\r\n    <style>\r\n        /* Base */\r\n        body {\r\n            color: #333;\r\n            font: 14px Verdana, \"Helvetica Neue\", helvetica, Arial, 'Microsoft YaHei', sans-serif;\r\n            margin: 0;\r\n            padding: 0 20px 20px;\r\n            word-break: break-word;\r\n        }\r\n        h1{\r\n            margin: 10px 0 0;\r\n            font-size: 28px;\r\n            font-weight: 500;\r\n            line-height: 32px;\r\n        }\r\n        h2{\r\n            color: #4288ce;\r\n            font-weight: 400;\r\n            padding: 6px 0;\r\n            margin: 6px 0 0;\r\n            font-size: 18px;\r\n            border-bottom: 1px solid #eee;\r\n        }\r\n        h3.subheading {\r\n            color: #4288ce;\r\n            margin: 6px 0 0;\r\n            font-weight: 400;\r\n        }\r\n        h3{\r\n            margin: 12px;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n        }\r\n        abbr{\r\n            cursor: help;\r\n            text-decoration: underline;\r\n            text-decoration-style: dotted;\r\n        }\r\n        a{\r\n            color: #868686;\r\n            cursor: pointer;\r\n        }\r\n        a:hover{\r\n            text-decoration: underline;\r\n        }\r\n        .line-error{\r\n            background: #f8cbcb;\r\n        }\r\n\r\n        .echo table {\r\n            width: 100%;\r\n        }\r\n\r\n        .echo pre {\r\n            padding: 16px;\r\n            overflow: auto;\r\n            font-size: 85%;\r\n            line-height: 1.45;\r\n            background-color: #f7f7f7;\r\n            border: 0;\r\n            border-radius: 3px;\r\n            font-family: Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\r\n        }\r\n\r\n        .echo pre > pre {\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        /* Layout */\r\n        .col-md-3 {\r\n            width: 25%;\r\n        }\r\n        .col-md-9 {\r\n            width: 75%;\r\n        }\r\n        [class^=\"col-md-\"] {\r\n            float: left;\r\n        }\r\n        .clearfix {\r\n            clear:both;\r\n        }\r\n        @media only screen\r\n        and (min-device-width : 375px)\r\n        and (max-device-width : 667px) {\r\n            .col-md-3,\r\n            .col-md-9 {\r\n                width: 100%;\r\n            }\r\n        }\r\n        /* Exception Info */\r\n        .bg_exception{\r\n            background:url(\"../../static/skin/tfy/img/500bg.png\") no-repeat;\r\n            background-size:100% 100%;\r\n            text-align: center;\r\n            padding-top:55px;\r\n            height:500px;\r\n        }\r\n        \r\n        .bg_exception .look{\r\n          margin-top:75px;\r\n          color:#fb5050;\r\n        }\r\n        .bg_exception .look a {\r\n            color:#fb5050;\r\n            display:inline-block;\r\n            margin-right: 6px;\r\n            font-size: 14px;\r\n            text-decoration-line: none;\r\n         }\r\n        .bg_exception .look a:hover{\r\n            font-size: 15px;\r\n            font-weight:700;\r\n         }\r\n        .bg_exception .look span{\r\n            font-size: 14px;\r\n            margin-right: 5px;           \r\n        }\r\n        .bg_exception>div{\r\n            border-top:0px;\r\n        }\r\n\r\n        .exception {\r\n            margin-top: 20px;\r\n        }\r\n        .exception .message{\r\n            padding: 12px;\r\n            border: 1px solid #ddd;\r\n            border-bottom: 0 none;\r\n            line-height: 18px;\r\n            font-size:16px;\r\n            border-top-left-radius: 4px;\r\n            border-top-right-radius: 4px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n\r\n        .exception .code{\r\n            float: left;\r\n            text-align: center;\r\n            color: #fff;\r\n            margin-right: 12px;\r\n            padding: 16px;\r\n            border-radius: 4px;\r\n            background: #999;\r\n        }\r\n        .exception .source-code{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n\r\n            background: #f9f9f9;\r\n            overflow-x: auto;\r\n\r\n        }\r\n        .exception .source-code pre{\r\n            margin: 0;\r\n        }\r\n        .exception .source-code pre ol{\r\n            margin: 0;\r\n            color: #4288ce;\r\n            display: inline-block;\r\n            min-width: 100%;\r\n            box-sizing: border-box;\r\n            font-size:14px;\r\n            font-family: \"Century Gothic\",Consolas,\"Liberation Mono\",Courier,Verdana;\r\n            padding-left: 40px;\r\n        }\r\n        .exception .source-code pre li{\r\n            border-left: 1px solid #ddd;\r\n            height: 18px;\r\n            line-height: 18px;\r\n        }\r\n        .exception .source-code pre code{\r\n            color: #333;\r\n            height: 100%;\r\n            display: inline-block;\r\n            border-left: 1px solid #fff;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n            border-top: 0 none;\r\n            line-height: 16px;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace ol{\r\n            margin: 12px;\r\n        }\r\n        .exception .trace ol li{\r\n            padding: 2px 4px;\r\n        }\r\n        .exception div:last-child{\r\n            border-bottom-left-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        /* Exception Variables */\r\n        .exception-var table{\r\n            width: 100%;\r\n            margin: 12px 0;\r\n            box-sizing: border-box;\r\n            table-layout:fixed;\r\n            word-wrap:break-word;\r\n        }\r\n        .exception-var table caption{\r\n            text-align: left;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            padding: 6px 0;\r\n        }\r\n        .exception-var table caption small{\r\n            font-weight: 300;\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n            color: #ccc;\r\n        }\r\n        .exception-var table tbody{\r\n            font-size: 13px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,\"微软雅黑\";\r\n        }\r\n        .exception-var table td{\r\n            padding: 0 6px;\r\n            vertical-align: top;\r\n            word-break: break-all;\r\n        }\r\n        .exception-var table td:first-child{\r\n            width: 28%;\r\n            font-weight: bold;\r\n            white-space: nowrap;\r\n        }\r\n        .exception-var table td pre{\r\n            margin: 0;\r\n        }\r\n\r\n        /* Copyright Info */\r\n        .copyright{\r\n            margin-top: 24px;\r\n            padding: 12px 0;\r\n            border-top: 1px solid #eee;\r\n        }\r\n\r\n        /* SPAN elements with the classes below are added by prettyprint. */\r\n        pre.prettyprint .pln { color: #000 }  /* plain text */\r\n        pre.prettyprint .str { color: #080 }  /* string content */\r\n        pre.prettyprint .kwd { color: #008 }  /* a keyword */\r\n        pre.prettyprint .com { color: #800 }  /* a comment */\r\n        pre.prettyprint .typ { color: #606 }  /* a type name */\r\n        pre.prettyprint .lit { color: #066 }  /* a literal value */\r\n        /* punctuation, lisp open bracket, lisp close bracket */\r\n        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }\r\n        pre.prettyprint .tag { color: #008 }  /* a markup tag name */\r\n        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */\r\n        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */\r\n        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */\r\n        pre.prettyprint .fun { color: red }  /* a function name */\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"echo\">\r\n            </div>\r\n        <div class=\"exception bg_exception\">\r\n\r\n            <div class=\"info\"><h1>您的访问出错了啦。。</h1></div>\r\n        <div>\r\n            <p class=\"look\">\r\n                                <span>您可以查看更多:</span>\r\n                                <a href=\"/info/list-1.html\">住宅出售</a>\r\n                                <a href=\"/shangye/\">商业办公</a>\r\n                                <a href=\"/shangpu/\">商铺</a>\r\n                                <a href=\"/info/list-2.html\">住宅出租</a>\r\n                                <a href=\"/shangpu/list-2.html\">商铺出租</a>\r\n                                <a href=\"/xiezilou/list-2.html\">写字楼</a>\r\n                            </p>\r\n        </div>\r\n\r\n    </div>\r\n    \r\n    \r\n    \r\n    <div class=\"copyright\">\r\n        <a title=\"官方网站\" href=\"http://www.tengfangyun.com\"></a>\r\n    </div>\r\n    </body>\r\n</html>\r\n", "httpCode": 500}
2025-07-29 11:46:30,688 - INFO - 推送链接https://www.tengfun.com/houseResource/saveHouseByLm,推送房源信息{"id": 9311, "trade_status": 9, "trade_type": 1, "total_floor": 6, "floor": 3, "loudong": "2号楼", "danyuan": "3单元", "mianji": 110, "fanghao": "503", "shi": 3, "ting": 2, "wei": 2, "rent_price": 0, "danjia": 20272, "is_top": 0, "sale_price": 2230000, "zhuangxiu": "毛坯", "chaoxiang": "南北", "agent_id": 5, "vr_url": "", "ctime": "2025-06-13 15:53:50", "utime": "2025-06-13 15:53:50", "label": "", "house_info": {"house_code": "", "house_structure": "", "building_type": "", "building_structure": "", "heating_type": "", "ownership": "", "property_certificate_period": "", "property_rights": "", "mortgage": "有抵押", "mortgage_price": 0, "elevator": "有电梯", "built_year": "", "elevator_desc": "1梯1户", "first_upload_at": "2025-06-13", "last_trade_at": "", "check_in_time": "", "check_in": "随时入住", "term": "", "see_time": "", "electricity": "民电", "water": "民水", "gas": "有", "term_type": "", "facilities": "", "parking": "有车位"}, "house_certificate": 0, "house_certificate_image": [], "house_follow_info": [], "community_name": "幸福西北区", "lat": 35.087132, "lng": 117.153954, "pic": [], "area": "滕州市", "region": "龙泉", "has_key": 1, "has_entrust": 0, "videos": [], "vrs": [], "tel": "13791416566"}
2025-07-29 11:46:31,146 - INFO - 推送房源信息返回结果{"content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>500</title>\r\n    <meta name=\"robots\" content=\"noindex,nofollow\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\r\n    <style>\r\n        /* Base */\r\n        body {\r\n            color: #333;\r\n            font: 14px Verdana, \"Helvetica Neue\", helvetica, Arial, 'Microsoft YaHei', sans-serif;\r\n            margin: 0;\r\n            padding: 0 20px 20px;\r\n            word-break: break-word;\r\n        }\r\n        h1{\r\n            margin: 10px 0 0;\r\n            font-size: 28px;\r\n            font-weight: 500;\r\n            line-height: 32px;\r\n        }\r\n        h2{\r\n            color: #4288ce;\r\n            font-weight: 400;\r\n            padding: 6px 0;\r\n            margin: 6px 0 0;\r\n            font-size: 18px;\r\n            border-bottom: 1px solid #eee;\r\n        }\r\n        h3.subheading {\r\n            color: #4288ce;\r\n            margin: 6px 0 0;\r\n            font-weight: 400;\r\n        }\r\n        h3{\r\n            margin: 12px;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n        }\r\n        abbr{\r\n            cursor: help;\r\n            text-decoration: underline;\r\n            text-decoration-style: dotted;\r\n        }\r\n        a{\r\n            color: #868686;\r\n            cursor: pointer;\r\n        }\r\n        a:hover{\r\n            text-decoration: underline;\r\n        }\r\n        .line-error{\r\n            background: #f8cbcb;\r\n        }\r\n\r\n        .echo table {\r\n            width: 100%;\r\n        }\r\n\r\n        .echo pre {\r\n            padding: 16px;\r\n            overflow: auto;\r\n            font-size: 85%;\r\n            line-height: 1.45;\r\n            background-color: #f7f7f7;\r\n            border: 0;\r\n            border-radius: 3px;\r\n            font-family: Consolas, \"Liberation Mono\", Menlo, Courier, monospace;\r\n        }\r\n\r\n        .echo pre > pre {\r\n            padding: 0;\r\n            margin: 0;\r\n        }\r\n        /* Layout */\r\n        .col-md-3 {\r\n            width: 25%;\r\n        }\r\n        .col-md-9 {\r\n            width: 75%;\r\n        }\r\n        [class^=\"col-md-\"] {\r\n            float: left;\r\n        }\r\n        .clearfix {\r\n            clear:both;\r\n        }\r\n        @media only screen\r\n        and (min-device-width : 375px)\r\n        and (max-device-width : 667px) {\r\n            .col-md-3,\r\n            .col-md-9 {\r\n                width: 100%;\r\n            }\r\n        }\r\n        /* Exception Info */\r\n        .bg_exception{\r\n            background:url(\"../../static/skin/tfy/img/500bg.png\") no-repeat;\r\n            background-size:100% 100%;\r\n            text-align: center;\r\n            padding-top:55px;\r\n            height:500px;\r\n        }\r\n        \r\n        .bg_exception .look{\r\n          margin-top:75px;\r\n          color:#fb5050;\r\n        }\r\n        .bg_exception .look a {\r\n            color:#fb5050;\r\n            display:inline-block;\r\n            margin-right: 6px;\r\n            font-size: 14px;\r\n            text-decoration-line: none;\r\n         }\r\n        .bg_exception .look a:hover{\r\n            font-size: 15px;\r\n            font-weight:700;\r\n         }\r\n        .bg_exception .look span{\r\n            font-size: 14px;\r\n            margin-right: 5px;           \r\n        }\r\n        .bg_exception>div{\r\n            border-top:0px;\r\n        }\r\n\r\n        .exception {\r\n            margin-top: 20px;\r\n        }\r\n        .exception .message{\r\n            padding: 12px;\r\n            border: 1px solid #ddd;\r\n            border-bottom: 0 none;\r\n            line-height: 18px;\r\n            font-size:16px;\r\n            border-top-left-radius: 4px;\r\n            border-top-right-radius: 4px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n\r\n        .exception .code{\r\n            float: left;\r\n            text-align: center;\r\n            color: #fff;\r\n            margin-right: 12px;\r\n            padding: 16px;\r\n            border-radius: 4px;\r\n            background: #999;\r\n        }\r\n        .exception .source-code{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n\r\n            background: #f9f9f9;\r\n            overflow-x: auto;\r\n\r\n        }\r\n        .exception .source-code pre{\r\n            margin: 0;\r\n        }\r\n        .exception .source-code pre ol{\r\n            margin: 0;\r\n            color: #4288ce;\r\n            display: inline-block;\r\n            min-width: 100%;\r\n            box-sizing: border-box;\r\n            font-size:14px;\r\n            font-family: \"Century Gothic\",Consolas,\"Liberation Mono\",Courier,Verdana;\r\n            padding-left: 40px;\r\n        }\r\n        .exception .source-code pre li{\r\n            border-left: 1px solid #ddd;\r\n            height: 18px;\r\n            line-height: 18px;\r\n        }\r\n        .exception .source-code pre code{\r\n            color: #333;\r\n            height: 100%;\r\n            display: inline-block;\r\n            border-left: 1px solid #fff;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace{\r\n            padding: 6px;\r\n            border: 1px solid #ddd;\r\n            border-top: 0 none;\r\n            line-height: 16px;\r\n        font-size:14px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,Verdana,\"微软雅黑\";\r\n        }\r\n        .exception .trace ol{\r\n            margin: 12px;\r\n        }\r\n        .exception .trace ol li{\r\n            padding: 2px 4px;\r\n        }\r\n        .exception div:last-child{\r\n            border-bottom-left-radius: 4px;\r\n            border-bottom-right-radius: 4px;\r\n        }\r\n\r\n        /* Exception Variables */\r\n        .exception-var table{\r\n            width: 100%;\r\n            margin: 12px 0;\r\n            box-sizing: border-box;\r\n            table-layout:fixed;\r\n            word-wrap:break-word;\r\n        }\r\n        .exception-var table caption{\r\n            text-align: left;\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n            padding: 6px 0;\r\n        }\r\n        .exception-var table caption small{\r\n            font-weight: 300;\r\n            display: inline-block;\r\n            margin-left: 10px;\r\n            color: #ccc;\r\n        }\r\n        .exception-var table tbody{\r\n            font-size: 13px;\r\n            font-family: Consolas,\"Liberation Mono\",Courier,\"微软雅黑\";\r\n        }\r\n        .exception-var table td{\r\n            padding: 0 6px;\r\n            vertical-align: top;\r\n            word-break: break-all;\r\n        }\r\n        .exception-var table td:first-child{\r\n            width: 28%;\r\n            font-weight: bold;\r\n            white-space: nowrap;\r\n        }\r\n        .exception-var table td pre{\r\n            margin: 0;\r\n        }\r\n\r\n        /* Copyright Info */\r\n        .copyright{\r\n            margin-top: 24px;\r\n            padding: 12px 0;\r\n            border-top: 1px solid #eee;\r\n        }\r\n\r\n        /* SPAN elements with the classes below are added by prettyprint. */\r\n        pre.prettyprint .pln { color: #000 }  /* plain text */\r\n        pre.prettyprint .str { color: #080 }  /* string content */\r\n        pre.prettyprint .kwd { color: #008 }  /* a keyword */\r\n        pre.prettyprint .com { color: #800 }  /* a comment */\r\n        pre.prettyprint .typ { color: #606 }  /* a type name */\r\n        pre.prettyprint .lit { color: #066 }  /* a literal value */\r\n        /* punctuation, lisp open bracket, lisp close bracket */\r\n        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }\r\n        pre.prettyprint .tag { color: #008 }  /* a markup tag name */\r\n        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */\r\n        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */\r\n        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */\r\n        pre.prettyprint .fun { color: red }  /* a function name */\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"echo\">\r\n            </div>\r\n        <div class=\"exception bg_exception\">\r\n\r\n            <div class=\"info\"><h1>您的访问出错了啦。。</h1></div>\r\n        <div>\r\n            <p class=\"look\">\r\n                                <span>您可以查看更多:</span>\r\n                                <a href=\"/info/list-1.html\">住宅出售</a>\r\n                                <a href=\"/shangye/\">商业办公</a>\r\n                                <a href=\"/shangpu/\">商铺</a>\r\n                                <a href=\"/info/list-2.html\">住宅出租</a>\r\n                                <a href=\"/shangpu/list-2.html\">商铺出租</a>\r\n                                <a href=\"/xiezilou/list-2.html\">写字楼</a>\r\n                            </p>\r\n        </div>\r\n\r\n    </div>\r\n    \r\n    \r\n    \r\n    <div class=\"copyright\">\r\n        <a title=\"官方网站\" href=\"http://www.tengfangyun.com\"></a>\r\n    </div>\r\n    </body>\r\n</html>\r\n", "httpCode": 500}

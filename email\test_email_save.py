#!/usr/bin/env python3
"""
测试邮件保存功能
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager, run_async

async def test_email_save():
    """测试邮件保存功能"""
    print("🧪 开始测试邮件保存功能...")
    
    # 初始化数据库管理器
    db_manager = DatabaseManager()
    
    # 初始化数据库
    success = await db_manager.init_database()
    print(f"数据库初始化: {'✅ 成功' if success else '❌ 失败'}")
    
    if not success:
        return
    
    # 添加测试邮箱
    test_email = "<EMAIL>"
    test_token = "test_token_123"
    test_cookies = "test_cookies_456"
    
    email_id = await db_manager.add_email(test_email, test_token, test_cookies)
    print(f"添加测试邮箱: {'✅ 成功' if email_id else '❌ 失败'} (ID: {email_id})")
    
    if not email_id:
        return
    
    # 模拟邮件数据
    test_message_data = {
        'from': '<EMAIL>',
        'subject': 'HHHHH测试',
        'content': '这是一封测试邮件的内容',
        'receivedAt': '2024-01-01T12:00:00Z'
    }
    
    # 保存邮件
    message_id = await db_manager.add_email_message(
        email_id=email_id,
        email_name=test_email,
        sender=test_message_data['from'],
        subject=test_message_data['subject'],
        content=test_message_data['content'],
        received_time=test_message_data['receivedAt']
    )
    
    print(f"保存测试邮件: {'✅ 成功' if message_id else '❌ 失败'} (ID: {message_id})")
    
    # 查询邮件列表
    messages = await db_manager.get_email_messages()
    print(f"查询邮件列表: 找到 {len(messages)} 封邮件")
    
    for msg in messages:
        print(f"  - 邮箱: {msg['email_name']}")
        print(f"    发件人: {msg['sender']}")
        print(f"    主题: {msg['subject']}")
        print(f"    时间: {msg['received_time']}")
        print(f"    内容: {msg['content'][:50]}...")
        print()
    
    print("🎉 测试完成!")

def main():
    """主函数"""
    asyncio.run(test_email_save())

if __name__ == "__main__":
    main()

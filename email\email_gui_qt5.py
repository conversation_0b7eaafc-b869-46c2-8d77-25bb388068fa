"""
现代化临时邮箱GUI应用 - PyQt5版本
采用现代设计风格，支持邮箱管理、邮件监控、数据库操作等功能
"""

import sys
import os
import threading
import time
import asyncio
from datetime import datetime
from typing import Dict, List, Optional
import pyperclip

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QTabWidget, QTableWidget, QTableWidgetItem, QTextEdit, QLineEdit,
    QPushButton, QLabel, QStatusBar, QSplitter, QHeaderView, QMessageBox,
    QProgressBar, QFrame, QScrollArea, QGroupBox, QCheckBox, QSpacerItem,
    QSizePolicy, QDialog, QDialogButtonBox, QFormLayout, QComboBox
)
from PyQt5.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSize, QPropertyAnimation, 
    QEasingCurve, QRect, pyqtSlot
)
from PyQt5.QtGui import (
    QFont, QIcon, QPalette, QColor, QPixmap, QPainter, QBrush, 
    QLinearGradient
)

# 导入现有模块
from singleuse_email_api import SingleUseEmailAPI
from database import DatabaseManager, run_async


class ModernButton(QPushButton):
    """现代化按钮组件"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setMinimumHeight(40)
        self.setFont(QFont("Segoe UI", 10))
        self.setCursor(Qt.PointingHandCursor)


class CopyButton(QPushButton):
    """一键复制按钮组件"""

    def __init__(self, text_to_copy="", tooltip="复制", parent=None):
        super().__init__("📋", parent)
        self.text_to_copy = text_to_copy
        self.setToolTip(tooltip)
        self.setMinimumSize(20, 20)
        self.setMaximumSize(20, 20)
        self.setCursor(Qt.PointingHandCursor)
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #f1f5f9);
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                font-size: 11px;
                font-weight: 500;
                padding: 4px;
                color: #64748b;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #eff6ff, stop:1 #dbeafe);
                border-color: #60a5fa;
                color: #3b82f6;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dbeafe, stop:1 #bfdbfe);
                border-color: #3b82f6;
                color: #1e40af;
            }
        """)
        self.clicked.connect(self.copy_text)

    def copy_text(self):
        """复制文本到剪贴板"""
        try:
            if self.text_to_copy:
                pyperclip.copy(self.text_to_copy)
                # 临时改变按钮文本显示复制成功
                original_text = self.text()
                self.setText("✓")
                self.setStyleSheet(self.styleSheet() + """
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #f0fdf4, stop:1 #dcfce7);
                        border-color: #22c55e;
                        color: #16a34a;
                    }
                """)
                # 1秒后恢复原状
                QTimer.singleShot(1000, lambda: self.reset_button(original_text))
        except Exception as e:
            print(f"复制失败: {e}")

    def reset_button(self, original_text):
        """重置按钮状态"""
        self.setText(original_text)
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #f1f5f9);
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                font-size: 11px;
                font-weight: 500;
                padding: 4px;
                color: #64748b;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #eff6ff, stop:1 #dbeafe);
                border-color: #60a5fa;
                color: #3b82f6;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #dbeafe, stop:1 #bfdbfe);
                border-color: #3b82f6;
                color: #1e40af;
            }
        """)

    def update_text(self, new_text):
        """更新要复制的文本"""
        self.text_to_copy = new_text


class CopyableTextWidget(QWidget):
    """可复制的文本显示组件"""

    def __init__(self, text="", copy_tooltip="复制", parent=None):
        super().__init__(parent)
        self.setup_ui(text, copy_tooltip)

    def setup_ui(self, text, copy_tooltip):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(8)

        # 文本标签
        self.text_label = QLabel(text)
        self.text_label.setWordWrap(True)
        self.text_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        self.text_label.setMinimumHeight(24)
        self.text_label.setStyleSheet("""
            QLabel {
                padding: 4px 8px;
                background: rgba(248, 250, 252, 0.8);
                border: 1px solid rgba(226, 232, 240, 0.6);
                border-radius: 6px;
                font-size: 12px;
                color: #334155;
                line-height: 1.4;
            }
        """)
        layout.addWidget(self.text_label, 1)

        # 复制按钮
        self.copy_btn = CopyButton(text, copy_tooltip)
        self.copy_btn.setMinimumSize(24, 24)
        self.copy_btn.setMaximumSize(24, 24)
        layout.addWidget(self.copy_btn)

    def set_text(self, text):
        """设置文本"""
        self.text_label.setText(text)
        self.copy_btn.update_text(text)

    def get_text(self):
        """获取文本"""
        return self.text_label.text()


class EmailCard(QFrame):
    """邮箱卡片组件"""
    
    def __init__(self, email_data: Dict, parent=None):
        super().__init__(parent)
        self.email_data = email_data
        self.setup_ui()
    
    def setup_ui(self):
        """设置卡片UI"""
        self.setFrameStyle(QFrame.Box)
        self.setMinimumHeight(80)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # 邮箱地址
        email_label = QLabel(self.email_data.get('email_name', ''))
        email_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        email_label.setStyleSheet("color: #1e40af; margin-bottom: 4px;")
        layout.addWidget(email_label)
        
        # 创建时间
        created_time = self.email_data.get('created_at', '')
        if created_time:
            time_label = QLabel(f"创建时间: {created_time}")
            time_label.setFont(QFont("Segoe UI", 9))
            time_label.setStyleSheet("color: #64748b;")
            layout.addWidget(time_label)
        
        # 状态信息
        status_layout = QHBoxLayout()
        
        # 邮件数量
        email_count = self.email_data.get('email_count', 0)
        count_label = QLabel(f"邮件: {email_count}")
        count_label.setFont(QFont("Segoe UI", 9))
        count_label.setStyleSheet("color: #059669; font-weight: 500;")
        status_layout.addWidget(count_label)
        
        status_layout.addStretch()
        
        # 复制按钮
        copy_btn = CopyButton(
            self.email_data.get('email_name', ''),
            "复制邮箱地址"
        )
        status_layout.addWidget(copy_btn)
        
        layout.addLayout(status_layout)
        
        # 设置卡片样式
        self.setStyleSheet("""
            EmailCard {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8fafc);
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                margin: 4px;
            }
            EmailCard:hover {
                border-color: #cbd5e1;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f5f9);
            }
        """)


class EmailWorkerThread(QThread):
    """邮箱获取工作线程"""

    email_generated = pyqtSignal(str, str, str)  # 邮箱地址, token, cookies
    error_occurred = pyqtSignal(str)  # 错误信息

    def run(self):
        """获取邮箱"""
        try:
            api = SingleUseEmailAPI()
            email, messages, token, cookies = api.get_email_with_session()

            if email and token and cookies:
                self.email_generated.emit(email, token, cookies)
            else:
                self.error_occurred.emit("无法获取邮箱地址或session信息")

        except Exception as e:
            self.error_occurred.emit(str(e))


class EmailMonitorThread(QThread):
    """邮件监控线程"""

    new_email_received = pyqtSignal(dict, str)  # 邮件数据, 邮箱地址
    status_updated = pyqtSignal(str)  # 状态更新
    error_occurred = pyqtSignal(str)  # 错误信息

    def __init__(self, email_address: str, api_service: SingleUseEmailAPI, email_id: int = None):
        super().__init__()
        self.email_address = email_address
        self.api_service = api_service
        self.email_id = email_id
        self.monitoring = False
        self.received_emails = set()

    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.start()

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.quit()
        self.wait()

    def run(self):
        """监控线程主循环"""
        print(f"🚀 开始监控邮箱: {self.email_address}")

        while self.monitoring:
            try:
                emails = self.api_service.get_messages()

                new_emails = []
                for email in emails:
                    email_id = email.get('id', f"{email.get('from', '')}_{email.get('subject', '')}_{email.get('receivedAt', '')}")
                    if email_id not in self.received_emails:
                        self.received_emails.add(email_id)
                        new_emails.append(email)

                for email in new_emails:
                    self.new_email_received.emit(email, self.email_address)

                # 等待10秒
                for _ in range(100):
                    if not self.monitoring:
                        print(f"🛑 监控标志已设为False，退出等待循环: {self.email_address}")
                        break
                    self.msleep(100)  # 100ms

            except Exception as e:
                error_msg = f"监控邮件时发生错误: {str(e)}"
                print(f"❌ 监控线程异常: {error_msg}")

                # 如果是token相关错误，停止监控
                if "419" in str(e) or "token" in str(e).lower() or "proxy reauthentication" in str(e):
                    print(f"❌ 邮箱 {self.email_address} token失效，设置monitoring=False")
                    self.monitoring = False
                    self.error_occurred.emit(f"Token失效，监控已停止: {error_msg}")
                    print(f"🛑 即将退出监控循环: {self.email_address}")
                    break
                else:
                    self.error_occurred.emit(error_msg)
                    print(f"⏳ 非token错误，等待10秒后重试: {self.email_address}")
                    self.msleep(10000)  # 等待10秒后重试

        print(f"✅ 监控线程已退出: {self.email_address}")


class DatabaseInitThread(QThread):
    """数据库初始化线程"""
    
    init_completed = pyqtSignal(bool)  # 初始化完成信号
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    def run(self):
        """执行数据库初始化"""
        try:
            # 检查表是否存在
            tables_exist = run_async(self.db_manager.check_tables_exist())
            
            if not tables_exist:
                # 创建表
                success = run_async(self.db_manager.init_database())
                self.init_completed.emit(success)
            else:
                self.init_completed.emit(True)
                
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            self.init_completed.emit(False)


class EmailManagementWidget(QWidget):
    """邮箱管理页面"""

    # 定义信号
    refresh_email_messages_signal = pyqtSignal()

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.monitor_threads = {}  # 存储监控线程
        self.setup_ui()
        self.load_emails()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(24)
        layout.setContentsMargins(32, 32, 32, 32)
        
        # 顶部操作栏
        top_layout = QHBoxLayout()
        top_layout.setSpacing(16)

        # 获取新邮箱按钮（主要操作）
        self.get_email_btn = QPushButton("📧 获取新邮箱")
        self.get_email_btn.setMinimumHeight(48)
        self.get_email_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40a9ff, stop:1 #1890ff);
                color: white;
                border: 1px solid #1890ff;
                padding: 12px 32px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                min-width: 160px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #69c0ff, stop:1 #40a9ff);
                border-color: #40a9ff;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1890ff, stop:1 #096dd9);
                border-color: #096dd9;
            }
            QPushButton:disabled {
                background: #f5f5f5;
                color: #bfbfbf;
                border-color: #d9d9d9;
            }
        """)
        self.get_email_btn.clicked.connect(self.get_new_email)
        top_layout.addWidget(self.get_email_btn)

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.setMinimumHeight(48)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8fafc);
                color: #374151;
                border: 1px solid #d1d5db;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f9fafb, stop:1 #f3f4f6);
                border-color: #9ca3af;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f3f4f6, stop:1 #e5e7eb);
                border-color: #6b7280;
            }
        """)
        self.refresh_btn.clicked.connect(self.load_emails)
        top_layout.addWidget(self.refresh_btn)

        top_layout.addStretch()
        layout.addLayout(top_layout)

        # 邮箱列表区域
        self.emails_scroll = QScrollArea()
        self.emails_scroll.setWidgetResizable(True)
        self.emails_scroll.setMinimumHeight(400)
        self.emails_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                background: #ffffff;
            }
            QScrollArea QWidget {
                background: #ffffff;
            }
        """)

        # 邮箱容器
        self.emails_container = QWidget()
        self.emails_layout = QVBoxLayout(self.emails_container)
        self.emails_layout.setSpacing(12)
        self.emails_layout.setContentsMargins(16, 16, 16, 16)
        self.emails_layout.addStretch()

        self.emails_scroll.setWidget(self.emails_container)
        layout.addWidget(self.emails_scroll)

        # 状态栏
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 14px;
                padding: 8px 0;
            }
        """)
        layout.addWidget(self.status_label)

    def load_emails(self):
        """加载邮箱列表"""
        try:
            self.status_label.setText("正在加载邮箱...")
            
            # 清空现有邮箱
            for i in reversed(range(self.emails_layout.count())):
                item = self.emails_layout.itemAt(i)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.setParent(None)
                    else:
                        # 如果是spacer item，跳过
                        continue

            # 从数据库获取邮箱
            emails = run_async(self.db_manager.get_all_emails())
            
            if not emails:
                no_email_label = QLabel("暂无邮箱，点击上方按钮获取新邮箱")
                no_email_label.setAlignment(Qt.AlignCenter)
                no_email_label.setStyleSheet("""
                    QLabel {
                        color: #9ca3af;
                        font-size: 16px;
                        padding: 40px;
                        background: #f9fafb;
                        border: 2px dashed #d1d5db;
                        border-radius: 8px;
                        margin: 20px;
                    }
                """)
                self.emails_layout.insertWidget(0, no_email_label)
            else:
                for email_data in emails:
                    email_widget = self.create_email_widget(email_data)
                    self.emails_layout.insertWidget(self.emails_layout.count() - 1, email_widget)

            self.status_label.setText(f"已加载 {len(emails)} 个邮箱")

        except Exception as e:
            self.status_label.setText(f"加载失败: {str(e)}")
            print(f"加载邮箱失败: {e}")

    def create_email_widget(self, email_data: Dict) -> QWidget:
        """创建邮箱显示组件"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Box)
        widget.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8fafc);
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                margin: 4px;
                padding: 8px;
            }
            QFrame:hover {
                border-color: #cbd5e1;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f5f9);
            }
        """)
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(12)

        # 顶部信息行
        top_row = QHBoxLayout()
        
        # 邮箱地址（可复制）
        email_widget = CopyableTextWidget(
            email_data.get('email_name', ''),
            "复制邮箱地址"
        )
        top_row.addWidget(email_widget, 1)

        # 状态指示器
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(8)

        # 监控状态
        email_id = email_data.get('id')
        is_monitoring = email_id in self.monitor_threads and self.monitor_threads[email_id].monitoring
        
        if is_monitoring:
            status_label = QLabel("🟢 监控中")
            status_label.setStyleSheet("color: #059669; font-weight: 500;")
        else:
            status_label = QLabel("⚪ 未监控")
            status_label.setStyleSheet("color: #6b7280; font-weight: 500;")
        
        status_layout.addWidget(status_label)
        top_row.addWidget(status_widget)

        layout.addLayout(top_row)

        # 详细信息行
        info_layout = QHBoxLayout()
        
        # 创建时间
        created_time = email_data.get('created_at', '')
        if created_time:
            time_label = QLabel(f"创建: {created_time}")
            time_label.setStyleSheet("color: #64748b; font-size: 12px;")
            info_layout.addWidget(time_label)

        # 邮件数量
        email_count = email_data.get('email_count', 0)
        count_label = QLabel(f"邮件: {email_count}")
        count_label.setStyleSheet("color: #059669; font-size: 12px; font-weight: 500;")
        info_layout.addWidget(count_label)

        info_layout.addStretch()
        layout.addLayout(info_layout)

        # 操作按钮行
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)

        # 监控按钮
        if is_monitoring:
            monitor_btn = QPushButton("⏹️ 停止监控")
            monitor_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ef4444, stop:1 #dc2626);
                    color: white;
                    border: 1px solid #dc2626;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f87171, stop:1 #ef4444);
                }
            """)
            monitor_btn.clicked.connect(lambda: self.stop_monitoring(email_data))
        else:
            monitor_btn = QPushButton("👁️ 开始监控")
            monitor_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #10b981, stop:1 #059669);
                    color: white;
                    border: 1px solid #059669;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #34d399, stop:1 #10b981);
                }
            """)
            monitor_btn.clicked.connect(lambda: self.start_monitoring(email_data))

        buttons_layout.addWidget(monitor_btn)

        # 删除按钮
        delete_btn = QPushButton("🗑️ 删除")
        delete_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8fafc);
                color: #ef4444;
                border: 1px solid #fecaca;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fef2f2, stop:1 #fee2e2);
                border-color: #fca5a5;
            }
        """)
        delete_btn.clicked.connect(lambda: self.delete_email(email_data))
        buttons_layout.addWidget(delete_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        return widget

    def get_new_email(self):
        """获取新邮箱"""
        self.get_email_btn.setEnabled(False)
        self.get_email_btn.setText("🔄 获取中...")
        self.status_label.setText("正在获取新邮箱...")

        # 创建工作线程
        self.email_worker = EmailWorkerThread()
        self.email_worker.email_generated.connect(self.on_email_generated)
        self.email_worker.error_occurred.connect(self.on_email_error)
        self.email_worker.start()

    def on_email_generated(self, email: str, token: str, cookies: str):
        """邮箱获取成功"""
        try:
            # 保存到数据库
            success = run_async(self.db_manager.save_email(email, token, cookies))
            
            if success:
                self.status_label.setText(f"成功获取邮箱: {email}")
                self.load_emails()  # 重新加载列表
            else:
                self.status_label.setText("保存邮箱失败")

        except Exception as e:
            self.status_label.setText(f"保存失败: {str(e)}")
            print(f"保存邮箱失败: {e}")
        
        finally:
            self.get_email_btn.setEnabled(True)
            self.get_email_btn.setText("📧 获取新邮箱")

    def on_email_error(self, error: str):
        """邮箱获取失败"""
        self.status_label.setText(f"获取失败: {error}")
        self.get_email_btn.setEnabled(True)
        self.get_email_btn.setText("📧 获取新邮箱")
        
        QMessageBox.warning(self, "获取失败", f"无法获取邮箱:\n{error}")

    def start_monitoring(self, email_data: Dict):
        """开始监控邮箱"""
        try:
            email_id = email_data.get('id')
            email_address = email_data.get('email_name')
            
            if email_id in self.monitor_threads:
                return
            
            # 创建API实例
            api = SingleUseEmailAPI()
            api.set_session(email_data.get('token', ''), email_data.get('cookies', ''))
            
            # 创建监控线程
            monitor_thread = EmailMonitorThread(email_address, api, email_id)
            monitor_thread.new_email_received.connect(self.on_new_email)
            monitor_thread.error_occurred.connect(self.on_monitor_error)
            
            self.monitor_threads[email_id] = monitor_thread
            monitor_thread.start_monitoring()
            
            self.status_label.setText(f"开始监控: {email_address}")
            self.load_emails()  # 刷新显示
            
        except Exception as e:
            self.status_label.setText(f"监控启动失败: {str(e)}")
            print(f"启动监控失败: {e}")

    def stop_monitoring(self, email_data: Dict):
        """停止监控邮箱"""
        try:
            email_id = email_data.get('id')
            email_address = email_data.get('email_name')
            
            if email_id in self.monitor_threads:
                self.monitor_threads[email_id].stop_monitoring()
                del self.monitor_threads[email_id]
                
                self.status_label.setText(f"停止监控: {email_address}")
                self.load_emails()  # 刷新显示
                
        except Exception as e:
            self.status_label.setText(f"停止监控失败: {str(e)}")
            print(f"停止监控失败: {e}")

    def delete_email(self, email_data: Dict):
        """删除邮箱"""
        email_address = email_data.get('email_name', '')
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除邮箱 {email_address} 吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 先停止监控
                email_id = email_data.get('id')
                if email_id in self.monitor_threads:
                    self.monitor_threads[email_id].stop_monitoring()
                    del self.monitor_threads[email_id]
                
                # 从数据库删除
                success = run_async(self.db_manager.delete_email(email_id))
                
                if success:
                    self.status_label.setText(f"已删除邮箱: {email_address}")
                    self.load_emails()  # 重新加载列表
                else:
                    self.status_label.setText("删除失败")
                    
            except Exception as e:
                self.status_label.setText(f"删除失败: {str(e)}")
                print(f"删除邮箱失败: {e}")

    def on_new_email(self, email_data: dict, email_address: str):
        """收到新邮件"""
        try:
            # 保存邮件到数据库
            run_async(self.db_manager.save_message(
                email_address=email_address,
                message_data=email_data
            ))
            
            # 发送信号通知刷新邮件列表
            self.refresh_email_messages_signal.emit()
            
            # 更新状态
            subject = email_data.get('subject', '无主题')
            sender = email_data.get('from', '未知发件人')
            self.status_label.setText(f"📧 新邮件: {subject} (来自: {sender})")
            
        except Exception as e:
            print(f"处理新邮件失败: {e}")

    def on_monitor_error(self, error: str):
        """监控错误处理"""
        self.status_label.setText(f"监控错误: {error}")
        print(f"监控错误: {error}")


class EmailMessagesWidget(QWidget):
    """邮件管理页面"""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_messages()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(24)
        layout.setContentsMargins(32, 32, 32, 32)

        # 顶部操作栏
        top_layout = QHBoxLayout()
        top_layout.setSpacing(16)

        # 刷新邮件按钮
        self.refresh_btn = QPushButton("🔄 刷新邮件")
        self.refresh_btn.setMinimumHeight(40)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #95de64, stop:1 #52c41a);
                color: white;
                border: 1px solid #52c41a;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #b7eb8f, stop:1 #73d13d);
                border-color: #73d13d;
            }
        """)
        self.refresh_btn.clicked.connect(self.load_messages)
        top_layout.addWidget(self.refresh_btn)

        # 清空邮件按钮
        self.clear_btn = QPushButton("🗑️ 清空所有邮件")
        self.clear_btn.setMinimumHeight(40)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff7875, stop:1 #ff4d4f);
                color: white;
                border: 1px solid #ff4d4f;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff9c99, stop:1 #ff7875);
                border-color: #ff7875;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_all_messages)
        top_layout.addWidget(self.clear_btn)

        top_layout.addStretch()

        # 邮箱筛选
        filter_label = QLabel("📧 筛选邮箱:")
        filter_label.setStyleSheet("font-weight: 500; color: #424242;")
        top_layout.addWidget(filter_label)

        self.email_filter = QComboBox()
        self.email_filter.addItem("所有邮箱", "")
        self.email_filter.setMinimumWidth(200)
        self.email_filter.currentTextChanged.connect(self.filter_messages)
        top_layout.addWidget(self.email_filter)

        layout.addLayout(top_layout)

        # 分割器
        splitter = QSplitter(Qt.Horizontal)

        # 邮件列表
        self.message_table = QTableWidget()
        self.message_table.setColumnCount(5)
        self.message_table.setHorizontalHeaderLabels([
            "邮箱", "发件人", "主题", "接收时间", "操作"
        ])

        # 设置表格样式
        header = self.message_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 邮箱
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 发件人
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # 主题
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 接收时间
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # 操作
        
        # 设置固定列宽
        header.resizeSection(0, 180)  # 邮箱列
        header.resizeSection(4, 120)  # 操作列

        self.message_table.setAlternatingRowColors(True)
        self.message_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.message_table.verticalHeader().setDefaultSectionSize(80)  # 增加行高
        self.message_table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)  # 行高自适应
        self.message_table.verticalHeader().setVisible(False)  # 隐藏行号
        self.message_table.setWordWrap(True)  # 启用文本换行
        self.message_table.itemSelectionChanged.connect(self.on_message_selected)

        splitter.addWidget(self.message_table)

        # 邮件内容显示
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        content_layout.addWidget(QLabel("邮件内容:"))

        self.content_display = QTextEdit()
        self.content_display.setReadOnly(True)
        content_layout.addWidget(self.content_display)

        splitter.addWidget(content_widget)
        splitter.setSizes([400, 400])

        layout.addWidget(splitter)

        # 加载邮箱列表到筛选器
        self.load_email_filter()

    def load_email_filter(self):
        """加载邮箱筛选列表"""
        try:
            emails = run_async(self.db_manager.get_all_emails())

            self.email_filter.clear()
            self.email_filter.addItem("所有邮箱", "")

            for email in emails:
                self.email_filter.addItem(email['email_name'], email['email_name'])

        except Exception as e:
            print(f"加载邮箱筛选列表失败: {e}")

    def load_messages(self):
        """加载邮件列表"""
        try:
            messages = run_async(self.db_manager.get_email_messages())

            self.message_table.setRowCount(len(messages))

            for row, message in enumerate(messages):
                # 邮箱
                self.message_table.setItem(row, 0, QTableWidgetItem(message['email_name']))

                # 发件人
                sender = message['sender'] or "未知发件人"
                self.message_table.setItem(row, 1, QTableWidgetItem(sender))

                # 主题
                subject = message['subject'] or "无主题"
                self.message_table.setItem(row, 2, QTableWidgetItem(subject))

                # 接收时间
                received_time = message['received_time']
                if received_time:
                    try:
                        received_time = datetime.fromisoformat(received_time).strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        pass
                self.message_table.setItem(row, 3, QTableWidgetItem(received_time or ""))

                # 操作按钮
                btn_widget = QWidget()
                btn_layout = QHBoxLayout(btn_widget)
                btn_layout.setContentsMargins(10, 8, 10, 8)
                btn_layout.setSpacing(6)

                # 删除按钮
                delete_btn = QPushButton("删除")
                delete_btn.setToolTip("删除邮件")
                delete_btn.setMinimumWidth(60)
                delete_btn.setMinimumHeight(36)
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #ff7875, stop:1 #ff4d4f);
                        color: white;
                        border: 1px solid #ff4d4f;
                        border-radius: 6px;
                        font-size: 12px;
                        font-weight: 500;
                        padding: 4px 8px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #ff9c99, stop:1 #ff7875);
                        border-color: #ff7875;
                    }
                """)
                delete_btn.clicked.connect(lambda: self.delete_message(message))
                btn_layout.addWidget(delete_btn)

                btn_layout.addStretch()
                self.message_table.setCellWidget(row, 4, btn_widget)

                # 存储消息数据
                self.message_table.item(row, 0).setData(Qt.UserRole, message)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载邮件列表失败: {str(e)}")

    def filter_messages(self):
        """筛选邮件"""
        filter_email = self.email_filter.currentData()

        for row in range(self.message_table.rowCount()):
            item = self.message_table.item(row, 0)
            if filter_email == "" or item.text() == filter_email:
                self.message_table.setRowHidden(row, False)
            else:
                self.message_table.setRowHidden(row, True)

    def on_message_selected(self):
        """邮件选中事件"""
        current_row = self.message_table.currentRow()
        if current_row >= 0:
            item = self.message_table.item(current_row, 0)
            if item:
                message_data = item.data(Qt.UserRole)
                if message_data:
                    content = message_data.get('content', '无内容')

                    # 格式化显示内容
                    formatted_content = f"""发件人: {message_data.get('sender', '未知')}
主题: {message_data.get('subject', '无主题')}
接收时间: {message_data.get('received_time', '未知')}
邮箱: {message_data.get('email_name', '未知')}

内容:
{content}
"""
                    self.content_display.setPlainText(formatted_content)

    def delete_message(self, message_data: Dict):
        """删除邮件"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除这封邮件吗？\n主题: {message_data.get('subject', '无主题')}",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            success = run_async(self.db_manager.delete_email_message(message_data['id']))

            if success:
                self.load_messages()
                QMessageBox.information(self, "成功", "邮件已删除")
            else:
                QMessageBox.critical(self, "错误", "删除邮件失败")

    def clear_all_messages(self):
        """清空所有邮件"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有邮件吗？此操作不可恢复。",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                messages = run_async(self.db_manager.get_email_messages())
                for message in messages:
                    run_async(self.db_manager.delete_email_message(message['id']))

                self.load_messages()
                QMessageBox.information(self, "成功", "所有邮件已清空")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"清空邮件失败: {str(e)}")


class SettingsWidget(QWidget):
    """设置页面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(28)
        layout.setContentsMargins(32, 32, 32, 32)

        # 应用设置组
        app_group = QGroupBox("应用设置")
        app_layout = QFormLayout(app_group)

        # 监控间隔
        self.monitor_interval = QComboBox()
        self.monitor_interval.addItems(["5秒", "10秒", "30秒", "1分钟", "5分钟"])
        self.monitor_interval.setCurrentText("10秒")
        app_layout.addRow("监控间隔:", self.monitor_interval)

        # 自动复制
        self.auto_copy = QCheckBox("获取邮箱时自动复制到剪贴板")
        self.auto_copy.setChecked(True)
        app_layout.addRow(self.auto_copy)

        # 新邮件通知
        self.email_notification = QCheckBox("收到新邮件时显示通知")
        self.email_notification.setChecked(True)
        app_layout.addRow(self.email_notification)

        layout.addWidget(app_group)

        # 数据库设置组
        db_group = QGroupBox("数据库设置")
        db_layout = QVBoxLayout(db_group)

        # 数据库路径
        db_path_layout = QHBoxLayout()
        self.db_path_label = QLabel("数据库路径: email/db/email_database.db")
        db_path_layout.addWidget(self.db_path_label)

        backup_btn = QPushButton("💾 备份数据库")
        backup_btn.setMinimumHeight(40)
        backup_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffd666, stop:1 #faad14);
                color: #262626;
                border: 1px solid #faad14;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffe58f, stop:1 #ffc53d);
                border-color: #ffc53d;
            }
        """)
        backup_btn.clicked.connect(self.backup_database)
        db_path_layout.addWidget(backup_btn)

        db_layout.addLayout(db_path_layout)

        layout.addWidget(db_group)

        # 关于信息
        about_group = QGroupBox("关于")
        about_layout = QVBoxLayout(about_group)

        about_text = QLabel("""
<h3>临时邮箱工具</h3>
<p><b>版本:</b> 1.0.0</p>
<p><b>作者:</b> 小威</p>
<p><b>描述:</b> 现代化的临时邮箱管理工具，支持邮箱获取、邮件监控、数据库存储等功能。</p>
<p><b>特性:</b></p>
<ul>
<li>🎨 现代化界面设计</li>
<li>📧 自动邮箱获取</li>
<li>👁️ 实时邮件监控</li>
<li>💾 数据库持久化存储</li>
<li>📋 一键复制功能</li>
<li>🔄 自动刷新机制</li>
</ul>
        """)
        about_text.setWordWrap(True)
        about_text.setOpenExternalLinks(True)
        about_layout.addWidget(about_text)

        layout.addWidget(about_group)

        layout.addStretch()

    def backup_database(self):
        """备份数据库"""
        try:
            import shutil
            from datetime import datetime
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"email_database_backup_{timestamp}.db"
            
            # 复制数据库文件
            source_path = "db/email_database.db"
            backup_path = f"db/{backup_filename}"
            
            if os.path.exists(source_path):
                shutil.copy2(source_path, backup_path)
                QMessageBox.information(
                    self, "备份成功", 
                    f"数据库已备份到:\n{backup_path}"
                )
            else:
                QMessageBox.warning(
                    self, "备份失败", 
                    "数据库文件不存在"
                )
                
        except Exception as e:
            QMessageBox.critical(
                self, "备份失败", 
                f"备份数据库时发生错误:\n{str(e)}"
            )


class EmailToolMainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.db_manager = None
        self.init_ui()
        self.init_database()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("临时邮箱工具")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 设置应用图标（如果有的话）
        try:
            self.setWindowIcon(QIcon("icon.ico"))
        except:
            pass

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建标题栏
        self.create_title_bar(main_layout)

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                background: #ffffff;
                border-radius: 8px;
                margin-top: -1px;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #f1f5f9);
                border: 1px solid #e2e8f0;
                border-bottom: none;
                padding: 12px 24px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                color: #64748b;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8fafc);
                border-color: #e0e0e0;
                color: #1e40af;
                font-weight: 600;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f1f5f9, stop:1 #e2e8f0);
                color: #475569;
            }
        """)

        main_layout.addWidget(self.tab_widget)

        # 创建状态栏
        self.create_status_bar()

        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8fafc, stop:1 #f1f5f9);
            }
        """)

    def create_title_bar(self, layout):
        """创建标题栏"""
        title_frame = QFrame()
        title_frame.setFixedHeight(80)
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e40af, stop:1 #1e3a8a);
                border: none;
                border-bottom: 2px solid #1d4ed8;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(32, 16, 32, 16)

        # 应用标题
        title_label = QLabel("📧 临时邮箱工具")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_layout.addWidget(title_label)

        # 版本信息
        version_label = QLabel("v1.0.0")
        version_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                background: transparent;
            }
        """)
        title_layout.addWidget(version_label)

        title_layout.addStretch()

        # 状态指示器
        self.connection_status = QLabel("🔴 未连接")
        self.connection_status.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: 500;
                background: rgba(255, 255, 255, 0.1);
                padding: 8px 16px;
                border-radius: 20px;
            }
        """)
        title_layout.addWidget(self.connection_status)

        layout.addWidget(title_frame)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_bar.addWidget(self.status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # 数据库状态
        self.db_status_label = QLabel("数据库: 未连接")
        self.status_bar.addPermanentWidget(self.db_status_label)

        # 设置状态栏样式
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background: #f8fafc;
                border-top: 1px solid #e2e8f0;
                color: #64748b;
                font-size: 12px;
            }
            QStatusBar QLabel {
                padding: 4px 8px;
            }
        """)

    def init_database(self):
        """初始化数据库"""
        try:
            self.status_label.setText("正在初始化数据库...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 无限进度条

            # 创建数据库管理器
            self.db_manager = DatabaseManager()

            # 创建数据库初始化线程
            self.db_init_thread = DatabaseInitThread(self.db_manager)
            self.db_init_thread.init_completed.connect(self.on_database_initialized)
            self.db_init_thread.start()

        except Exception as e:
            self.show_error("数据库初始化失败", str(e))

    def on_database_initialized(self, success: bool):
        """数据库初始化完成"""
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText("数据库初始化成功")
            self.db_status_label.setText("数据库: 已连接")
            self.connection_status.setText("🟢 已连接")
            self.connection_status.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 14px;
                    font-weight: 500;
                    background: rgba(34, 197, 94, 0.8);
                    padding: 8px 16px;
                    border-radius: 20px;
                }
            """)

            # 创建标签页
            self.create_tabs()

        else:
            self.status_label.setText("数据库初始化失败")
            self.db_status_label.setText("数据库: 连接失败")
            self.show_error("数据库错误", "无法初始化数据库，请检查数据库文件权限")

    def create_tabs(self):
        """创建标签页"""
        try:
            # 邮箱管理页面
            self.email_management = EmailManagementWidget(self.db_manager)
            self.tab_widget.addTab(self.email_management, "📧 邮箱管理")

            # 邮件查看页面
            self.email_messages = EmailMessagesWidget(self.db_manager)
            self.tab_widget.addTab(self.email_messages, "📬 邮件查看")

            # 设置页面
            self.settings = SettingsWidget()
            self.tab_widget.addTab(self.settings, "⚙️ 设置")

            # 连接信号
            self.email_management.refresh_email_messages_signal.connect(
                self.email_messages.load_messages
            )

            self.status_label.setText("界面初始化完成")

        except Exception as e:
            self.show_error("界面创建失败", str(e))

    def show_error(self, title: str, message: str):
        """显示错误对话框"""
        QMessageBox.critical(self, title, message)

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 停止所有监控线程
            if hasattr(self, 'email_management') and self.email_management:
                for thread in self.email_management.monitor_threads.values():
                    if thread.monitoring:
                        thread.stop_monitoring()

            # 关闭数据库连接
            if self.db_manager:
                # 这里可以添加数据库关闭逻辑
                pass

            event.accept()

        except Exception as e:
            print(f"关闭应用时发生错误: {e}")
            event.accept()


def apply_modern_style(app: QApplication):
    """应用现代化样式"""
    app.setStyle("Fusion")
    
    # 设置调色板
    palette = QPalette()
    
    # 窗口背景
    palette.setColor(QPalette.Window, QColor(248, 250, 252))
    palette.setColor(QPalette.WindowText, QColor(51, 65, 85))
    
    # 基础背景
    palette.setColor(QPalette.Base, QColor(255, 255, 255))
    palette.setColor(QPalette.AlternateBase, QColor(241, 245, 249))
    
    # 文本
    palette.setColor(QPalette.Text, QColor(15, 23, 42))
    palette.setColor(QPalette.BrightText, QColor(248, 250, 252))
    
    # 按钮
    palette.setColor(QPalette.Button, QColor(248, 250, 252))
    palette.setColor(QPalette.ButtonText, QColor(51, 65, 85))
    
    # 高亮
    palette.setColor(QPalette.Highlight, QColor(59, 130, 246))
    palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
    
    app.setPalette(palette)


def main():
    """主函数"""
    # 创建应用
    app = QApplication(sys.argv)
    app.setApplicationName("临时邮箱工具")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("EmailTool")

    # 应用现代化样式
    apply_modern_style(app)

    # 设置全局字体
    font = QFont()
    font.setFamilies(["Arial", "Microsoft YaHei", "SimSun"])
    app.setFont(font)

    try:
        # 创建主窗口
        window = EmailToolMainWindow()
        window.show()

        # 运行应用
        sys.exit(app.exec_())

    except Exception as e:
        print(f"应用启动失败: {e}")
        QMessageBox.critical(None, "启动错误", f"应用启动失败:\n{str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
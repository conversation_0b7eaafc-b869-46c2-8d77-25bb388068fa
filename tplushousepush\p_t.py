#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房源推送脚本
模拟PHP代码的房源推送功能
"""

import json
import requests
import logging
import urllib3
from typing import Dict, Any, Optional


class HousePushService:
    """房源推送服务类"""

    def __init__(self, base_url: str = "https://www.tengfun.com", log_level: int = logging.INFO,
                 verify_ssl: bool = False, disable_ssl_warnings: bool = True):
        """
        初始化房源推送服务

        Args:
            base_url: 推送站点的基础URL
            log_level: 日志级别
            verify_ssl: 是否验证SSL证书（默认False，模拟PHP行为）
            disable_ssl_warnings: 是否禁用SSL警告（默认True）
        """
        self.base_url = base_url
        self.push_endpoint = "/houseResource/saveHouseByLm"
        self.verify_ssl = verify_ssl
        self.session = requests.Session()

        # 设置默认请求头，模拟PHP的User-Agent
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1.13) Gecko/20080311 Firefox/2.0.0.13',
            'Content-Type': 'application/x-www-form-urlencoded'
        })

        # 处理SSL警告
        if not verify_ssl and disable_ssl_warnings:
            # 如果不验证SSL且要求禁用警告，则禁用警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 配置日志
        self._setup_logging(log_level)

    def _setup_logging(self, log_level: int):
        """设置日志配置"""
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('house_push.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def log(self, message: str):
        """记录日志"""
        self.logger.info(message)

    def my_curl(self, url: str, data: Optional[str] = None, method: str = 'get',
                verify_ssl: Optional[bool] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """
        模拟PHP的myCurl函数

        Args:
            url: 请求URL
            data: POST数据（JSON字符串）
            method: 请求方法
            verify_ssl: 是否验证SSL证书（None时使用实例设置）
            headers: 额外的请求头

        Returns:
            包含content和httpCode的字典
        """
        # 如果没有指定verify_ssl，使用实例的设置
        if verify_ssl is None:
            verify_ssl = self.verify_ssl
        try:
            # 设置额外的请求头
            if headers:
                request_headers = self.session.headers.copy()
                request_headers.update(headers)
            else:
                request_headers = self.session.headers

            # 发送请求
            if method.lower() == 'post':
                # 确保POST请求的Content-Type为application/x-www-form-urlencoded
                if 'Content-Type' not in request_headers:
                    request_headers['Content-Type'] = 'application/x-www-form-urlencoded'

                response = self.session.post(
                    url,
                    data=data,
                    verify=verify_ssl,
                    headers=request_headers,
                    timeout=30
                )
            else:
                response = self.session.get(
                    url,
                    verify=verify_ssl,
                    headers=request_headers,
                    timeout=30
                )

            # 解析响应内容
            try:
                content = response.json() if response.text else None
            except json.JSONDecodeError:
                content = response.text

            return {
                'content': content,
                'httpCode': response.status_code
            }

        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求异常: {str(e)}")
            return {
                'content': None,
                'httpCode': 0
            }

    def create_house_data(self) -> Dict[str, Any]:
        """
        创建房源数据结构（基于用户提供的参数）

        Returns:
            房源数据字典
        """
        house_data = {
            "id": 9311,
            "trade_status": 9,
            "trade_type": 1,
            "total_floor": 6,
            "floor": 3,
            "loudong": "2号楼",
            "danyuan": "3单元",
            "mianji": 110,
            "fanghao": "503",
            "shi": 3,
            "ting": 2,
            "wei": 2,
            "rent_price": 0,
            "danjia": 20272,
            "is_top": 0,
            "sale_price": 2230000,
            "zhuangxiu": "毛坯",
            "chaoxiang": "南北",
            "agent_id": 5,
            "vr_url": "",
            "ctime": "2025-06-13 15:53:50",
            "utime": "2025-06-13 15:53:50",
            "label": "",
            "house_info": {
                "house_code": "",
                "house_structure": "",
                "building_type": "",
                "building_structure": "",
                "heating_type": "",
                "ownership": "",
                "property_certificate_period": "",
                "property_rights": "",
                "mortgage": "有抵押",
                "mortgage_price": 0,
                "elevator": "有电梯",
                "built_year": "",
                "elevator_desc": "1梯1户",
                "first_upload_at": "2025-06-13",
                "last_trade_at": "",
                "check_in_time": "",
                "check_in": "随时入住",
                "term": "",
                "see_time": "",
                "electricity": "民电",
                "water": "民水",
                "gas": "有",
                "term_type": "",
                "facilities": "",
                "parking": "有车位"
            },
            "house_certificate": 0,
            "house_certificate_image": [],
            "house_follow_info": [],
            "community_name": "幸福西北区",
            "lat": 35.087132,
            "lng": 117.153954,
            "pic": [],
            "area": "滕州市",
            "region": "龙泉",
            "has_key": 1,
            "has_entrust": 0,
            "videos": [],
            "vrs": [],
            "tel": "13791416566"
        }

        return house_data

    def push_house_to_site(self, phone: str = "13791416566") -> Dict[str, Any]:
        """
        推送房源信息到站点

        Args:
            phone: 联系电话

        Returns:
            推送结果
        """
        # 获取房源数据
        house_data = self.create_house_data()

        # 设置电话号码
        house_data['tel'] = phone

        # 构建推送URL
        url = f"{self.base_url}{self.push_endpoint}"

        # 将房源数据转换为JSON字符串
        house_json = json.dumps(house_data, ensure_ascii=False)

        # 记录推送信息
        self.log(f"推送链接{url},推送房源信息{house_json}")

        # 发送POST请求
        result = self.my_curl(url, house_json, 'post')

        # 记录返回结果
        result_json = json.dumps(result, ensure_ascii=False)
        self.log(f"推送房源信息返回结果{result_json}")

        return result



def main():
    """
    主函数 - 演示房源推送功能
    """
    print("=== 房源推送脚本启动 ===")

    # 创建房源推送服务实例
    push_service = HousePushService()

    # 推送房源信息
    print("开始推送房源信息...")
    result = push_service.push_house_to_site("13791416566")

    # 输出结果
    print(f"推送完成，HTTP状态码: {result['httpCode']}")
    if result['content']:
        print(f"服务器响应: {json.dumps(result['content'], ensure_ascii=False, indent=2)}")
    else:
        print("服务器无响应内容")

    print("=== 房源推送脚本结束 ===")


if __name__ == "__main__":
    main()
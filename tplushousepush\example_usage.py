#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房源推送使用示例
"""

from p import HousePushService
import json


def example_basic_push():
    """基本推送示例"""
    print("=== 基本房源推送示例 ===")
    
    # 创建推送服务
    service = HousePushService()
    
    # 推送房源（使用默认电话号码）
    result = service.push_house_to_site()
    
    print(f"推送结果: HTTP {result['httpCode']}")
    if result['content']:
        print(f"响应内容: {json.dumps(result['content'], ensure_ascii=False, indent=2)}")


def example_custom_phone_push():
    """自定义电话号码推送示例"""
    print("\n=== 自定义电话号码推送示例 ===")
    
    # 创建推送服务
    service = HousePushService()
    
    # 使用自定义电话号码推送
    custom_phone = "18888888888"
    result = service.push_house_to_site(custom_phone)
    
    print(f"推送结果: HTTP {result['httpCode']}")
    if result['content']:
        print(f"响应内容: {json.dumps(result['content'], ensure_ascii=False, indent=2)}")


def example_get_house_data():
    """获取房源数据示例"""
    print("\n=== 获取房源数据示例 ===")

    # 创建推送服务
    service = HousePushService()

    # 获取房源数据（已包含完整的29张图片）
    house_data = service.create_house_data()
    print(f"房源数据包含 {len(house_data['pic'])} 张图片")
    print("房源基本信息:")
    basic_info = {k: v for k, v in house_data.items() if k not in ['pic', 'videos', 'house_info']}
    print(json.dumps(basic_info, ensure_ascii=False, indent=2))


def example_custom_base_url():
    """自定义基础URL示例"""
    print("\n=== 自定义基础URL推送示例 ===")
    
    # 使用自定义基础URL创建推送服务
    custom_service = HousePushService(base_url="https://test.example.com")
    
    # 推送房源
    result = custom_service.push_house_to_site("13900000000")
    
    print(f"推送结果: HTTP {result['httpCode']}")


if __name__ == "__main__":
    # 运行所有示例
    example_basic_push()
    example_custom_phone_push()
    example_get_house_data()
    example_custom_base_url()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房源推送脚本
模拟PHP代码的房源推送功能
"""

import json
import requests
import logging
import urllib3
from typing import Dict, Any, Optional


class HousePushService:
    """房源推送服务类"""

    def __init__(self, base_url: str = "https://yf.benew.cn", log_level: int = logging.INFO,
                 verify_ssl: bool = False, disable_ssl_warnings: bool = True):
        """
        初始化房源推送服务

        Args:
            base_url: 推送站点的基础URL
            log_level: 日志级别
            verify_ssl: 是否验证SSL证书（默认False，模拟PHP行为）
            disable_ssl_warnings: 是否禁用SSL警告（默认True）
        """
        self.base_url = base_url
        self.push_endpoint = "/houseResource/saveHouseByLm"
        self.verify_ssl = verify_ssl
        self.session = requests.Session()

        # 设置默认请求头，模拟PHP的User-Agent
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1.13) Gecko/20080311 Firefox/2.0.0.13',
            'Content-Type': 'application/json'
        })

        # 处理SSL警告
        if not verify_ssl and disable_ssl_warnings:
            # 如果不验证SSL且要求禁用警告，则禁用警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 配置日志
        self._setup_logging(log_level)

    def _setup_logging(self, log_level: int):
        """设置日志配置"""
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('house_push.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def log(self, message: str):
        """记录日志"""
        self.logger.info(message)

    def my_curl(self, url: str, data: Optional[str] = None, method: str = 'get',
                verify_ssl: Optional[bool] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """
        模拟PHP的myCurl函数

        Args:
            url: 请求URL
            data: POST数据（JSON字符串）
            method: 请求方法
            verify_ssl: 是否验证SSL证书（None时使用实例设置）
            headers: 额外的请求头

        Returns:
            包含content和httpCode的字典
        """
        # 如果没有指定verify_ssl，使用实例的设置
        if verify_ssl is None:
            verify_ssl = self.verify_ssl
        try:
            # 设置额外的请求头
            if headers:
                request_headers = self.session.headers.copy()
                request_headers.update(headers)
            else:
                request_headers = self.session.headers

            # 发送请求
            if method.lower() == 'post':
                response = self.session.post(
                    url,
                    data=data,
                    verify=verify_ssl,
                    headers=request_headers,
                    timeout=30
                )
            else:
                response = self.session.get(
                    url,
                    verify=verify_ssl,
                    headers=request_headers,
                    timeout=30
                )

            # 解析响应内容
            try:
                content = response.json() if response.text else None
            except json.JSONDecodeError:
                content = response.text

            return {
                'content': content,
                'httpCode': response.status_code
            }

        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求异常: {str(e)}")
            return {
                'content': None,
                'httpCode': 0
            }

    def create_house_data(self) -> Dict[str, Any]:
        """
        创建房源数据结构（基于日志中的示例数据）

        Returns:
            房源数据字典
        """
        house_data = {
            "id": 9774,
            "trade_status": 9,
            "trade_type": 1,
            "total_floor": 29,
            "floor": 25,
            "loudong": "19号楼",
            "danyuan": "1单元",
            "mianji": 140,
            "fanghao": "2503",
            "shi": 4,
            "ting": 2,
            "wei": 2,
            "rent_price": 0,
            "danjia": 11642,
            "is_top": 1,
            "sale_price": 1630000,
            "zhuangxiu": "毛坯",
            "chaoxiang": "南北",
            "agent_id": 2029,
            "vr_url": "",
            "ctime": "2025-07-26 17:25:24",
            "utime": "2025-07-26 17:25:24",
            "label": "",
            "house_info": {
                "house_code": "",
                "house_structure": "平层",
                "building_type": "板楼",
                "building_structure": "钢混结构",
                "heating_type": "",
                "ownership": "",
                "property_certificate_period": "满二",
                "property_rights": "",
                "mortgage": "有抵押",
                "mortgage_price": 0,
                "elevator": "有电梯",
                "built_year": "",
                "elevator_desc": "1梯1户",
                "first_upload_at": "2025-07-26",
                "last_trade_at": "",
                "check_in_time": "",
                "check_in": "随时入住",
                "term": "",
                "see_time": "",
                "electricity": "民电",
                "water": "民水",
                "gas": "有",
                "term_type": "",
                "facilities": "",
                "parking": "有车位"
            },
            "house_certificate": 0,
            "house_certificate_image": [],
            "house_follow_info": [],
            "community_name": "秦忆荣府北区",
            "lat": 30.056402,
            "lng": 119.914986,
            "pic": [
                {"id": 21102, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610695937942.jpg", "is_cover": 0},
                {"id": 21103, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610703103624.jpg", "is_cover": 0},
                {"id": 21104, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610710382027.jpg", "is_cover": 0},
                {"id": 21105, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610716110396.jpg", "is_cover": 0},
                {"id": 21106, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610723193924.jpg", "is_cover": 0},
                {"id": 21107, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610730730293.jpg", "is_cover": 0},
                {"id": 21108, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610736632632.jpg", "is_cover": 0},
                {"id": 21109, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610744846931.jpg", "is_cover": 0},
                {"id": 21110, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610753524420.jpg", "is_cover": 0},
                {"id": 21111, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610761585325.jpg", "is_cover": 0},
                {"id": 21112, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610772688484.jpg", "is_cover": 0},
                {"id": 21113, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610779931725.jpg", "is_cover": 0},
                {"id": 21121, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610805809515.jpg", "is_cover": 0},
                {"id": 21122, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610813845875.jpg", "is_cover": 0},
                {"id": 21123, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610822886818.jpg", "is_cover": 0},
                {"id": 21124, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610830911803.jpg", "is_cover": 0},
                {"id": 21125, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610838414886.jpg", "is_cover": 0},
                {"id": 21126, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610857761772.jpg", "is_cover": 0},
                {"id": 21127, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610867393927.jpg", "is_cover": 0},
                {"id": 21128, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610880350922.jpg", "is_cover": 0},
                {"id": 21129, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610889186535.jpg", "is_cover": 0},
                {"id": 21130, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610897399382.jpg", "is_cover": 0},
                {"id": 21131, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610905911945.jpg", "is_cover": 0},
                {"id": 21145, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610916101365.jpg", "is_cover": 0},
                {"id": 21146, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610927173454.jpg", "is_cover": 0},
                {"id": 21147, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610936370395.jpg", "is_cover": 0},
                {"id": 21148, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610949672374.jpg", "is_cover": 0},
                {"id": 21149, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610958743648.jpg", "is_cover": 0},
                {"id": 21150, "category_id": 1, "descp": "", "url": "https://img.tfcs.cn/1/1137/company/tmp/tmp_1753610966954899.jpg", "is_cover": 0}
            ],
            "area": "富阳区",
            "region": "富春",
            "has_key": 0,
            "has_entrust": 0,
            "videos": [
                {
                    "url": "https://img.tfcs.cn/1/1137/im/video/tmp/tmp_1753610977688644.mp4"
                }
            ],
            "vrs": [],
            "tel": "13175004384" 
        }

        return house_data

    def push_house_to_site(self, phone: str = "13175004384") -> Dict[str, Any]:
        """
        推送房源信息到站点

        Args:
            phone: 联系电话

        Returns:
            推送结果
        """
        # 获取房源数据
        house_data = self.create_house_data()

        # 设置电话号码
        house_data['tel'] = phone

        # 构建推送URL
        url = f"{self.base_url}{self.push_endpoint}"

        # 将房源数据转换为JSON字符串
        house_json = json.dumps(house_data, ensure_ascii=False)

        # 记录推送信息
        self.log(f"推送链接{url},推送房源信息{house_json}")

        # 发送POST请求
        result = self.my_curl(url, house_json, 'post')

        # 记录返回结果
        result_json = json.dumps(result, ensure_ascii=False)
        self.log(f"推送房源信息返回结果{result_json}")

        return result



def main():
    """
    主函数 - 演示房源推送功能
    """
    print("=== 房源推送脚本启动 ===")

    # 创建房源推送服务实例
    push_service = HousePushService()

    # 推送房源信息
    print("开始推送房源信息...")
    result = push_service.push_house_to_site("13175004384")

    # 输出结果
    print(f"推送完成，HTTP状态码: {result['httpCode']}")
    if result['content']:
        print(f"服务器响应: {json.dumps(result['content'], ensure_ascii=False, indent=2)}")
    else:
        print("服务器无响应内容")

    print("=== 房源推送脚本结束 ===")


if __name__ == "__main__":
    main()